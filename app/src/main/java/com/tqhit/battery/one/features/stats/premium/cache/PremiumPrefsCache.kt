package com.tqhit.battery.one.features.stats.premium.cache

import android.util.Log
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import javax.inject.Inject
import javax.inject.Singleton

interface PremiumCache {
    fun savePremiumActivationTime()
    fun isPremium(): Boolean
    fun getWatchedAdsCount(): Int
    fun setWatchedAdsCount(count: Int)
    fun getRemainingPremiumTime(): Pair<Int, Int>

}

/**
 * Implements PremiumCache using PreferencesHelper.
 * Premium lasts for 24h (1 day).
 */
@Singleton
class PremiumPrefsCache @Inject constructor(
    private val preferencesHelper: PreferencesHelper
) : PremiumCache {


    companion object {
        private const val TAG = "PremiumPrefsCache"
        private const val KEY_PREMIUM_ACTIVATED_AT = "key_premium_activated_at"
        private const val PREMIUM_DURATION_MS = 24 * 60 * 60 * 1000L // 24h
        private const val KEY_WATCHED_ADS_COUNT = "key_watched_ads_count"

    }


    override fun savePremiumActivationTime() {
        try {
            val now = System.currentTimeMillis()
            preferencesHelper.saveLong(KEY_PREMIUM_ACTIVATED_AT, now)
            Log.d(TAG, "Saved premium activation time=$now")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save premium time", e)
        }
    }

    override fun isPremium(): Boolean {
        return try {
            val activatedAt = preferencesHelper.getLong(KEY_PREMIUM_ACTIVATED_AT, 0L)
            val now = System.currentTimeMillis()
            val isActive = activatedAt != 0L && (now - activatedAt) <= PREMIUM_DURATION_MS

            Log.d(TAG, "Premium status: $isActive (activatedAt=$activatedAt)")
            isActive
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check premium status", e)
            false
        }
    }

    override fun getWatchedAdsCount(): Int {
        return preferencesHelper.getInt(KEY_WATCHED_ADS_COUNT, 0)
    }

    override fun setWatchedAdsCount(count: Int) {
        preferencesHelper.saveInt(KEY_WATCHED_ADS_COUNT, count)
    }

    override fun getRemainingPremiumTime(): Pair<Int, Int> {
        val activatedAt = preferencesHelper.getLong(KEY_PREMIUM_ACTIVATED_AT, 0L)
        val now = System.currentTimeMillis()
        val elapsed = now - activatedAt
        val remaining = PREMIUM_DURATION_MS - elapsed

        val hours = (remaining / (60 * 60 * 1000)).toInt().coerceAtLeast(0)
        val minutes = ((remaining % (60 * 60 * 1000)) / (60 * 1000)).toInt().coerceAtLeast(0)

        return Pair(hours, minutes)
    }




}
