package com.tqhit.battery.one.activity.starting

import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.viewbinding.ViewBinding
import com.google.gson.Gson
import com.ironsource.nu
import com.tqhit.adlib.sdk.ads.AdmobHelper
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.BatteryApplication
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.activity.onboarding.OnboardingAskPermissionActivity
import com.tqhit.battery.one.activity.onboarding.OnboardingFinalActivity
import com.tqhit.battery.one.activity.onboarding.OnboardingSuccessActivity
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
import com.tqhit.battery.one.base.LocaleAwareActivity
import com.tqhit.battery.one.databinding.ActivityStartingBinding
import com.tqhit.battery.one.databinding.ItemSlideLayout1NewBinding
import com.tqhit.battery.one.databinding.ItemSlideLayout2Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout3NewBinding
import com.tqhit.battery.one.databinding.ItemSlideLayout4NewBinding
import com.tqhit.battery.one.databinding.ItemSlideLayoutNativeFullscreenBinding
import com.tqhit.battery.one.features.stats.premium.cache.PremiumCache
import com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory
import com.tqhit.battery.one.utils.DateTimeUtils
import com.tqhit.battery.one.utils.OverlayPermissionUtils
import com.tqhit.battery.one.utils.VideoUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@AndroidEntryPoint
class StartingActivity : LocaleAwareActivity<ActivityStartingBinding>() {
    override val binding by lazy { ActivityStartingBinding.inflate(layoutInflater) }

    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    @Inject lateinit var applovinNativeAdManager: ApplovinNativeAdManager
    @Inject lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper
    @Inject lateinit var analyticsTracker: AnalyticsTracker
    @Inject lateinit var admobHelper: AdmobHelper
    @Inject
    lateinit var premiumCache: PremiumCache

    private val timeHandler = Handler(Looper.getMainLooper())
    private val timeRunnable = object : Runnable {
        override fun run() {
            updateTimeAndDate()
            timeHandler.postDelayed(this, 60_000L)
        }
    }

    private val appViewModel: AppViewModel by viewModels()

    private var player: ExoPlayer? = null

    private val animationViewModel: AnimationViewModel by viewModels()
    private var mediaUrl = ""

    @Inject lateinit var videoUtils: VideoUtils


    private var currentDotRunnable: Runnable? = null
    private var currentProgressRunnable: Runnable? = null
    private var currentButtonRunnable: Runnable? = null
    private var swipeRunnable: Runnable? = null

    private var handlerDots: Handler? = null
    private var handlerProgress: Handler? = null
    private var handlerButtons: Handler? = null
    private var handlerSwipe: Handler? = null

    var isVideoRemoteReady = false

    companion object {
        private const val TAG = "StartingActivity"
    }

    private val baseLayouts = listOf(
        R.layout.item_slide_layout_1_new,
        R.layout.item_slide_layout_3_new,
        R.layout.item_slide_layout_native_fullscreen,
        R.layout.item_slide_layout_4_new
    )

    private val layouts: List<Int> by lazy {
        if (premiumCache.isPremium()) {
            baseLayouts.filter { it != R.layout.item_slide_layout_native_fullscreen }
        } else {
            baseLayouts
        }
    }



    private val views = arrayListOf<ViewBinding>()

    private fun updateTimeAndDate() {
        val binding = if (views.size <= 3) views[2] as ItemSlideLayout4NewBinding else views[3] as ItemSlideLayout4NewBinding
        binding.textTime.text = DateTimeUtils.getCurrentTimeString()
        binding.textDate.text = DateTimeUtils.getCurrentDateString()
    }

    private val startingViewAdapter by lazy {
        StartingViewAdapter(views, applovinNativeAdManager, remoteConfigHelper, this, admobHelper)
    }

    private fun navigateToAskPermission() {
        val intent = Intent(this, OnboardingAskPermissionActivity::class.java)
        intent.putExtra("media_url", mediaUrl)
        startActivity(intent)
        finish()
    }

    private fun navigateToSuccess() {
        val intent = Intent(this, OnboardingSuccessActivity::class.java)
        intent.putExtra("media_url", mediaUrl)
        startActivity(intent)
        finish()
    }

    private fun navigateToFinal() {
        startActivity(Intent(this, OnboardingFinalActivity::class.java))
        finish()
    }

    override fun setupData() {
        super.setupData()

        // Check if we need to create a locale-aware LayoutInflater
        val savedLanguage = if (isThingInitialized) appRepository.getLanguage() else ""

        // Use the default LayoutInflater since the activity context is already locale-aware
        val localeAwareInflater = layoutInflater

        for ((index, layout) in layouts.withIndex()) {
            val binding = when (layout) {
                R.layout.item_slide_layout_1_new -> {
                    val slideBinding = ItemSlideLayout1NewBinding.inflate(localeAwareInflater)
                    slideBinding
                }
                R.layout.item_slide_layout_3_new -> {
                    val slideBinding = ItemSlideLayout3NewBinding.inflate(localeAwareInflater)
                    slideBinding
                }
                R.layout.item_slide_layout_native_fullscreen -> {
                    val slideBinding = ItemSlideLayoutNativeFullscreenBinding.inflate(localeAwareInflater)
                    slideBinding
                }
                R.layout.item_slide_layout_4_new -> {
                    val slideBinding = ItemSlideLayout4NewBinding.inflate(localeAwareInflater)
                    slideBinding
                }
                else -> break
            }
            views.add(binding)
        }

        // prevent back press
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {}
        }
        onBackPressedDispatcher.addCallback(this, callback)
    }

    override fun setupUI() {
        super.setupUI()

        binding.slidePager.adapter = startingViewAdapter
        binding.springDotsIndicator.attachTo(binding.slidePager)

        val layout4Binding = if (views.size <= 3) views[2] as ItemSlideLayout4NewBinding else views[3] as ItemSlideLayout4NewBinding
        getRandomAnimationVideoUrl()
        setupPlayer(layout4Binding, mediaUrl)
        updateTimeAndDate()
    }




    private fun startDotAnimation(
        textView: TextView,
        dotDelay: Long = 300L
    ) {
        currentDotRunnable?.let { handlerDots?.removeCallbacks(it) }

        handlerDots = Handler(mainLooper)
        val localHandler = handlerDots!!

        var dotCount = 1
        val dotRunnable = object : Runnable {
            override fun run() {
                textView.text = ".".repeat(dotCount)
                dotCount = if (dotCount >= 6) 1 else dotCount + 1
                localHandler.postDelayed(this, dotDelay)
            }
        }

        currentDotRunnable = dotRunnable
        localHandler.post(dotRunnable)
    }


    private fun startProgressAnimation(
        progressBar: ProgressBar,
        animatedDots: TextView,
        textView: TextView,
        finalText: String,
        progressDelay: Long = 10L
    ) {
        currentProgressRunnable?.let { handlerProgress?.removeCallbacks(it) }

        handlerProgress = Handler(mainLooper)
        val localHandler = handlerProgress!!

        var progress = 0
        val progressRunnable = object : Runnable {
            override fun run() {
                if (progress <= 100) {
                    progressBar.progress = progress
                    progress += 5
                    localHandler.postDelayed(this, 40L)
                } else {
                    animatedDots.visibility = View.GONE
                    textView.text = finalText
                }
            }
        }

        currentProgressRunnable = progressRunnable
        localHandler.post(progressRunnable)
    }


    fun startItemAnimation() {
        val layout3Binding = views[1] as ItemSlideLayout3NewBinding
        val buttons = listOf(
            layout3Binding.typeItem1,
            layout3Binding.typeItem2,
            layout3Binding.typeItem3,
            layout3Binding.typeItem4
        )
        startBouncingButtonsInOrder(buttons)
    }

    private fun startBouncingButtonsInOrder(buttons: List<View>) {
        currentButtonRunnable?.let { handlerButtons?.removeCallbacks(it) }

        handlerButtons = Handler(mainLooper)
        val localHandler = handlerButtons!!

        val animation = AnimationUtils.loadAnimation(this, R.anim.bounce_up_down)
        var currentIndex = 0

        val runnable = object : Runnable {
            override fun run() {
                if (buttons.isNotEmpty()) {
                    buttons[currentIndex].startAnimation(animation)
                    currentIndex = (currentIndex + 1) % buttons.size
                    localHandler.postDelayed(this, 1000)
                }
            }
        }

        currentButtonRunnable = runnable
        localHandler.post(runnable)
    }


    fun startAnimation(
        dotsView: TextView,
        textView: TextView,
        progressBar: ProgressBar,
        baseText: String = getString(R.string.we_are_choosing_one_for_you),
        finalText: String = getString(R.string.we_ve_picked_an_animation_for_you),
        dotDelay: Long = 300L,
        progressDelay: Long = 10L
    ) {
        stopAllAnimations()
        startDotAnimation(dotsView, dotDelay)
        startItemAnimation()
        startProgressAnimation(
            progressBar,
            dotsView,
            textView,
            finalText,
            progressDelay
        )
    }


    private fun stopAllAnimations() {
        currentDotRunnable?.let { handlerDots?.removeCallbacks(it) }
        currentProgressRunnable?.let { handlerProgress?.removeCallbacks(it) }
        currentButtonRunnable?.let { handlerButtons?.removeCallbacks(it) }

        currentDotRunnable = null
        currentProgressRunnable = null
        currentButtonRunnable = null
    }


    private fun getRandomAnimationVideoUrl() {
        try {
            val jsonString = remoteConfigHelper.getString("animation_json")

            if (jsonString.isBlank()) {
                Log.w("AnimationSetup", "Remote config JSON is blank")
                return
            }

            val categories = Gson().fromJson(jsonString, Array<AnimationCategory>::class.java).toList()

            val validItems = categories
                .filter { it.name.isNotBlank() && it.content.isNotEmpty() }
                .flatMap { it.content }

            if (validItems.isEmpty()) {
                Log.w("AnimationSetup", "No valid animation items found")
                return
            }

            val itemRandom = validItems.random()
            mediaUrl = itemRandom.mediaOriginal
        } catch (e: Exception) {
            Log.e("AnimationSetup", "Error parsing animation data", e)
        }
    }

    @OptIn(UnstableApi::class)
    private fun setupPlayer(binding: ItemSlideLayout4NewBinding, videoUrl: String) {
        try {

            player?.release()
            player = null
            player = ExoPlayer.Builder(binding.root.context).build().apply {

                addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        val stateString = when (playbackState) {
                            Player.STATE_IDLE -> "IDLE"
                            Player.STATE_BUFFERING -> "BUFFERING"
                            Player.STATE_READY -> {
                                isVideoRemoteReady = true
                                "READY"
                            }
                            Player.STATE_ENDED -> "ENDED"
                            else -> "UNKNOWN"
                        }
                        Log.d("VideoPlayer", "Playback state changed: $stateString")
                    }

                    override fun onPlayerError(error: PlaybackException) {
                        Log.e("VideoPlayer", "Player error: ${error.message}")
                    }
                })

                setMediaItem(MediaItem.fromUri(videoUrl))
                repeatMode = ExoPlayer.REPEAT_MODE_ONE
                playWhenReady = true
                prepare()
            }
            binding.playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM

            binding.playerView.player = player
                binding.playerView.useController = false


        } catch (e: Exception) {
            Log.e("AnimationSetup", "Error setting up player", e)
        }
    }





    override fun setupListener() {
        super.setupListener()

        val layout1Binding = views[0] as ItemSlideLayout1NewBinding
        startingViewAdapter.setupNextButton(layout1Binding)

        layout1Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)

        }

        val layout3Binding = views[1] as ItemSlideLayout3NewBinding
        startingViewAdapter.setupNextButton(layout3Binding)

        layout3Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout4Binding = if (views.size <= 3) views[2] as ItemSlideLayout4NewBinding else views[3] as ItemSlideLayout4NewBinding

        layout4Binding.secondaryAction.setOnClickListener {
            navigateToFinal()
        }

        layout4Binding.primaryAction.setOnClickListener {
            acceptAnimation()
            if(OverlayPermissionUtils.isOverlayPermissionGranted(this)){
                navigateToSuccess()
            } else {
                navigateToAskPermission()
            }
        }

        binding.slidePager.clearOnPageChangeListeners()
        binding.slidePager.addOnPageChangeListener(onPageChangeListener)
        binding.slidePager.setCurrentItem(0, true)
        onPageChangeListener.onPageSelected(0)
    }

    private val onPageChangeListener = object : androidx.viewpager.widget.ViewPager.OnPageChangeListener {
        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {}

        override fun onPageSelected(position: Int) {
            handlerSwipe?.removeCallbacksAndMessages(null)
            if (position <= 2) {
                handlerSwipe = Handler(Looper.getMainLooper())
                swipeRunnable = Runnable {
                    binding.slidePager.setCurrentItem(position + 1, true)
                }
                handlerSwipe?.postDelayed(swipeRunnable!!, 3000)
            }

            if (views[position] is ItemSlideLayout3NewBinding) {
                val layout = views[1] as ItemSlideLayout3NewBinding
                startAnimation(
                    layout.animatedDots,
                    layout.selectingText,
                    layout.progressBar
                )
                val view = startingViewAdapter.getViewAt(position)

                if (position >= 1) {
                    applovinInterstitialAdManager.loadInterstitialAd()
                }
                view?.let {
                    startingViewAdapter.setupNextButton(it)
                }
            }

            if (views[position] is ItemSlideLayout4NewBinding) {
                if (!isVideoRemoteReady) {
                    val layout4Binding = views[position] as ItemSlideLayout4NewBinding
                    Log.d("VideoPlayer", "Remote video not ready -> use local")
                    playLocalFallbackVideo(layout4Binding)
                } else if (player?.isPlaying == false) {
                    player?.playWhenReady = true
                    player?.play()
                }
            } else {
                if (player?.isPlaying == true)
                    player?.pause()
            }

            analyticsTracker.logEvent("fragment_onboarding_${position+1}")
        }
        override fun onPageScrollStateChanged(state: Int) {}
    }

    @OptIn(UnstableApi::class)
    private val localVideoUri: String
        get() = "android.resource://${this.packageName}/${R.raw.local_video_animation}"


    @OptIn(UnstableApi::class)
    private fun playLocalFallbackVideo(binding: ItemSlideLayout4NewBinding) {
        mediaUrl = localVideoUri
        player?.release()
        player = null
        player = ExoPlayer.Builder(binding.root.context).build().apply {
            setMediaItem(MediaItem.fromUri(localVideoUri))
            repeatMode = ExoPlayer.REPEAT_MODE_ONE
            playWhenReady = true
            prepare()
        }
        binding.playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
        binding.playerView.player = player
        binding.playerView.useController = false
    }


    private fun acceptAnimation() {
        animationViewModel.applyAnimation(mediaUrl)
        proceedNotOverlayPermission()
    }

    private fun proceedNotOverlayPermission() {

        // 1. Extract the dynamic filename from the URL
        val dynamicFileName = VideoUtils.getFileNameFromUrl(mediaUrl)
        val destinationFile = File(filesDir, dynamicFileName)

        // --- INSTANT ACTION ---
        // 2. Save the FULL PATH with the DYNAMIC filename for permanent storage
        appRepository.setVideoPath(destinationFile.absolutePath)
        // Also save the URL for immediate playback fallback
        appRepository.setAppliedAnimationUrl(mediaUrl)
        appRepository.setAnimationOverlayEnabled(true)
        animationViewModel.setApplied(mediaUrl)

        lifecycleScope.launch {
            try {
                // Use enhanced video utils for better performance with preloaded files
                val destFile = videoUtils.downloadAndSaveVideoEnhanced(mediaUrl,dynamicFileName)
                appRepository.setVideoPath(destFile.absolutePath)
                if (!isFinishing && !isDestroyed) {
                    Toast.makeText(
                        this@StartingActivity,
                        getString(R.string.video_saved),
                        Toast.LENGTH_SHORT
                    ).show()
                } else {
                    finish()
                }
                animationViewModel.setApplied(mediaUrl)
            } catch (e: Exception) {
                if (!isFinishing && !isDestroyed) {
                    Toast.makeText(
                        this@StartingActivity,
                        getString(R.string.error_accessing_file),
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        if (player?.isPlaying == true)
            player?.pause()

        handlerSwipe?.removeCallbacksAndMessages(null)
    }

    override fun onResume() {
        super.onResume()
        if (views.size > binding.slidePager.currentItem && views[binding.slidePager.currentItem] is ItemSlideLayout4NewBinding) {
            if (player?.isPlaying == false) {
                player?.playWhenReady = true
                player?.play()
            }
        }

        binding.slidePager.setCurrentItem(binding.slidePager.currentItem, true)
        onPageChangeListener.onPageSelected(binding.slidePager.currentItem)
    }

    override fun onDestroy() {
        super.onDestroy()
        player?.release()
        player = null
    }
}
