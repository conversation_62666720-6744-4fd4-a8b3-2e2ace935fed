<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/grey">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/action_group"
        android:orientation="vertical"
        android:gravity="center">
        <TextView
            android:id="@+id/title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/your_little_guide"
            android:layout_marginTop="@dimen/_20sdp"
            android:textColor="?attr/black"
            android:textSize="@dimen/_20ssp"
            android:textStyle="bold"
            android:layout_gravity="center"/>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:paddingVertical="@dimen/_10sdp"
            android:paddingHorizontal="@dimen/_20sdp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Step 1 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/_1_select_installed_apps_or_downloaded_app_in_setting"
                    android:textColor="?attr/black"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:adjustViewBounds="true"
                    android:background="@drawable/step_1" />

                <!-- Step 2 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/_2_select_battery_charging_animation_3d"
                    android:textColor="?attr/black"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:adjustViewBounds="true"
                    android:background="@drawable/step_2" />

                <!-- Step 3 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/_3_select_display_over_other_apps"
                    android:textColor="?attr/black"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_gravity="center"

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:adjustViewBounds="true"
                    android:background="@drawable/step_3" />

                <!-- Step 4 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/_4_turn_on"
                    android:textColor="?attr/black"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_gravity="center"

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:adjustViewBounds="true"
                    android:background="@drawable/step_4" />

            </LinearLayout>
        </ScrollView>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/action_group"
        android:paddingVertical="@dimen/_10sdp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/grey_block"
        app:layout_constraintBottom_toTopOf="@id/nativeAd">

        <TextView
            android:id="@+id/secondary_action"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_30sdp"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_toStartOf="@id/primary_layout"
            android:paddingHorizontal="@dimen/_10sdp"
            android:text="@string/i_will_do_it_later"
            android:textColor="?attr/black"
            android:textSize="@dimen/_11ssp"
            android:gravity="center" />

        <LinearLayout
            android:id="@+id/primary_layout"
            android:layout_width="@dimen/_120sdp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="9dp"
            android:animateLayoutChanges="true"
            android:background="@drawable/white_block"
            android:orientation="vertical">

            <Button
                android:id="@+id/primary_action"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/grey_block"
                android:longClickable="false"
                android:paddingStart="10dp"
                android:paddingTop="12.5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="12.5dp"
                android:text="@string/i_got_it"
                android:textAllCaps="false"
                android:textColor="?attr/black"
                android:textSize="@dimen/_15ssp" />
        </LinearLayout>
    </RelativeLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/nativeAd"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/grey" >
    </com.facebook.shimmer.ShimmerFrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

    <!--<LinearLayout-->
<!--    xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    android:layout_width="match_parent"-->
<!--    android:background="?attr/grey"-->
<!--    android:layout_height="wrap_content"-->
<!--    android:orientation="vertical">-->
<!--    <include-->
<!--        android:id="@+id/include_back_navigation"-->
<!--        layout="@layout/layout_back_navigation" />-->

<!--    <TextView-->

<!--        android:layout_marginTop="20dp"-->
<!--        android:paddingHorizontal="16dp"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="You must turn “Display over other Apps” on to use Animation Charging"-->
<!--        android:textColor="?attr/black"-->
<!--        android:textSize="16dp"-->
<!--        android:textStyle="bold" />-->
<!--<ScrollView-->
<!--    android:background="?attr/grey"-->
<!--    android:layout_width="match_parent"-->
<!--    android:layout_height="0dp"-->
<!--    android:layout_weight="1"-->
<!--    android:padding="16dp">-->

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:orientation="vertical">-->



<!--        &lt;!&ndash; Step 1 &ndash;&gt;-->
<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="1. Select “Installed Apps” or “Downloaded App” in Setting"-->
<!--            android:textColor="?attr/black"-->
<!--            android:textStyle="bold" />-->

<!--        <ImageView-->
<!--            android:layout_gravity="center"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="8dp"-->
<!--            android:adjustViewBounds="true"-->
<!--            android:background="@drawable/step_1" />-->

<!--        &lt;!&ndash; Step 2 &ndash;&gt;-->
<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="24dp"-->
<!--            android:text="2. Select “Battery Charging Animation 3D”"-->
<!--            android:textColor="?attr/black"-->
<!--            android:textStyle="bold" />-->

<!--        <ImageView-->
<!--            android:layout_gravity="center"-->

<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="8dp"-->
<!--            android:adjustViewBounds="true"-->
<!--            android:background="@drawable/step_2" />-->

<!--        &lt;!&ndash; Step 3 &ndash;&gt;-->
<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="24dp"-->
<!--            android:text="3. Select “Display over other Apps”"-->
<!--            android:textColor="?attr/black"-->
<!--            android:textStyle="bold" />-->

<!--        <ImageView-->
<!--            android:layout_gravity="center"-->

<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="8dp"-->
<!--            android:adjustViewBounds="true"-->
<!--            android:background="@drawable/step_3" />-->

<!--        &lt;!&ndash; Step 4 &ndash;&gt;-->
<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="24dp"-->
<!--            android:text="4. Turn On"-->
<!--            android:textColor="?attr/black"-->
<!--            android:textStyle="bold" />-->

<!--        <ImageView-->
<!--            android:layout_gravity="center"-->

<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="8dp"-->
<!--            android:adjustViewBounds="true"-->
<!--            android:background="@drawable/step_4" />-->

<!--    </LinearLayout>-->
<!--</ScrollView>-->
<!--    &lt;!&ndash; Button &ndash;&gt;-->
<!--    <LinearLayout-->
<!--        android:layout_margin="@dimen/_16sdp"-->
<!--        android:layout_gravity="center"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:animateLayoutChanges="true"-->
<!--        android:background="@drawable/white_block"-->
<!--        android:orientation="vertical">-->

<!--        <Button-->
<!--            android:textSize="@dimen/_14ssp"-->
<!--            android:layout_marginStart="8dp"-->
<!--            android:layout_marginEnd="8dp"-->
<!--            android:textColor="?attr/black"-->
<!--            android:id="@+id/goToSettingButton"-->
<!--            android:background="@drawable/grey_block"-->
<!--            android:paddingTop="12.5dp"-->
<!--            android:paddingBottom="12.5dp"-->
<!--            android:longClickable="false"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="8dp"-->
<!--            android:layout_marginBottom="8dp"-->
<!--            android:text="Go to Overlay Permission Setting"-->
<!--            android:textAllCaps="false"-->
<!--            android:paddingStart="10dp"-->
<!--            android:paddingEnd="10dp"-->
<!--            style="@style/Widget.AppCompat.Button.Borderless"/>-->
<!--    </LinearLayout>-->
<!--</LinearLayout>-->
