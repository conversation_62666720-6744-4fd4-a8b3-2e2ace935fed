package com.tqhit.battery.one

import android.app.Activity
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.webkit.WebView
import androidx.appcompat.app.AppCompatDelegate
import com.applovin.sdk.AppLovinSdk
import com.ironsource.mediationsdk.IronSource
import com.tqhit.adlib.sdk.AdLibHiltApplication
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.adlib.sdk.utils.Constant
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.manager.theme.ThemeManager
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper
import com.tqhit.battery.one.service.ChargingOverlayServiceHelper
import com.tqhit.battery.one.repository.AnimationPreloadingRepository
import com.tqhit.battery.one.service.animation.AnimationDataService
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.utils.ForegroundServiceUtils
import com.tqhit.battery.one.utils.PreloadingMonitor
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject
import androidx.core.net.toUri
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import com.applovin.sdk.AppLovinMediationProvider
import com.applovin.sdk.AppLovinPrivacySettings
import com.applovin.sdk.AppLovinSdkConfiguration.ConsentFlowUserGeography
import com.applovin.sdk.AppLovinSdkInitializationConfiguration
import com.facebook.ads.AdSettings
import com.google.firebase.FirebaseApp
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ProcessLifecycleOwner
import com.tqhit.battery.one.ads.manager.AppOpenAdManager
import com.tqhit.battery.one.features.stats.premium.cache.PremiumCache


@HiltAndroidApp
class BatteryApplication : AdLibHiltApplication(), LifecycleObserver {

    // Application-level coroutine scope for async initialization
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    @Inject
    lateinit var preferencesHelper: PreferencesHelper
    @Inject
    lateinit var appRepository: AppRepository
    @Inject
    lateinit var applovinNativeAdManager: com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
    @Inject
    lateinit var applovinRewardedAdManager: com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager
    @Inject
    lateinit var applovinInterstitialAdManager: com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
    @Inject
    lateinit var applovinBannerAdManager: com.tqhit.battery.one.ads.core.ApplovinBannerAdManager
    // Legacy batteryStatusServiceHelper injection removed - service deprecated
    @Inject
    lateinit var coreBatteryServiceHelper: CoreBatteryServiceHelper
    @Inject
    lateinit var chargingOverlayServiceHelper: ChargingOverlayServiceHelper
    @Inject
    lateinit var animationPreloadingRepository: AnimationPreloadingRepository
    @Inject
    lateinit var animationDataService: AnimationDataService
    @Inject
    lateinit var preloadingMonitor: PreloadingMonitor
    @Inject
    lateinit var deferredThumbnailPreloadingService: com.tqhit.battery.one.service.thumbnail.DeferredThumbnailPreloadingService
    @Inject
    lateinit var premiumCache: PremiumCache
    @Inject
    lateinit var appOpenAdManager: AppOpenAdManager

    companion object {
        private const val TAG = "BatteryApplication"
        var appSession: Int = 0
        var appOpenTime: Long = System.currentTimeMillis()
        @Volatile
        var isMaxSdkInitialized = false

        val nativeOnboarding: MutableLiveData<Any> = MutableLiveData()
    }

    /**
     * Public getter for MAX SDK initialization status
     */
    val isMaxSdkInitialized: Boolean
        get() = BatteryApplication.isMaxSdkInitialized

    override fun onCreate() {
        val startTime = System.currentTimeMillis()
        BatteryLogger.d(TAG, "STARTUP_TIMING: BatteryApplication.onCreate() started at $startTime")

        // Initialize Firebase synchronously before super.onCreate() to ensure Hilt has access to it.
        val firebaseStartTime = System.currentTimeMillis()
        try {
            FirebaseApp.initializeApp(this@BatteryApplication)
            val firebaseDuration = System.currentTimeMillis() - firebaseStartTime
            Log.d(TAG, "STARTUP_TIMING: Firebase init (sync) completed in ${firebaseDuration}ms")
        } catch (e: Exception) {
            Log.e(TAG, "CRITICAL: Firebase initialization failed", e)
        }

        // Now, Hilt can be initialized safely.
        val superStartTime = System.currentTimeMillis()
        super.onCreate()

        // Because this premiumCache is lazy-initialized, we need to ensure it is initialized before any other operations
        premiumCache.isPremium()

        BatteryLogger.logTiming(TAG, "super.onCreate()", System.currentTimeMillis() - superStartTime)
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)

        // Critical session tracking (keep synchronous for immediate availability)
        val prefsStartTime = System.currentTimeMillis()
        appSession = preferencesHelper.getInt("session", 0)
        appSession++
        preferencesHelper.saveInt("session", appSession)
        BatteryLogger.logTiming(TAG, "Preferences operations", System.currentTimeMillis() - prefsStartTime)

        // WebView setup (keep synchronous as it's needed for UI)
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val process = getProcessName()
                if (packageName != process) WebView.setDataDirectorySuffix(process)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error: ${e.message}")
        }

        // Move heavy initialization to background thread
        initializeAsyncComponents()

        Log.d(TAG, "STARTUP_TIMING: BatteryApplication.onCreate() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Initialize heavy components asynchronously to avoid blocking app startup
     */
    private fun initializeAsyncComponents() {
        applicationScope.launch(Dispatchers.IO) {
            val asyncStartTime = System.currentTimeMillis()
            Log.d(TAG, "STARTUP_TIMING: Async initialization started")

            try {
                // Battery services startup (can be deferred)
                val servicesStartTime = System.currentTimeMillis()
                startBatteryStatusService()
                startCoreBatteryStatsService()
                startChargingOverlayService()
                Log.d(TAG, "STARTUP_TIMING: Async battery services startup took ${System.currentTimeMillis() - servicesStartTime}ms")

                // PRELOAD_DISABLED: Animation preloading startup commented out to reduce resource consumption
                // This improves cold start performance and reduces memory usage during app initialization
                // Animation loading will still work on-demand when charging occurs
                /*
                val preloadStartTime = System.currentTimeMillis()
                startAnimationPreloading()
                Log.d(TAG, "STARTUP_TIMING: Animation preloading startup took ${System.currentTimeMillis() - preloadStartTime}ms")
                */
                Log.d(TAG, "STARTUP_TIMING: Animation preloading disabled for performance optimization")

                // PRELOAD_DISABLED: Deferred thumbnail preloading commented out to reduce resource consumption
                // This improves cold start performance and reduces memory usage during app initialization
                // Thumbnail loading will still work on-demand when needed
                /*
                val thumbnailPreloadStartTime = System.currentTimeMillis()
                deferredThumbnailPreloadingService.initiateDeferredPreloading()
                Log.d(TAG, "STARTUP_TIMING: Deferred thumbnail preloading initiated in ${System.currentTimeMillis() - thumbnailPreloadStartTime}ms")
                */
                Log.d(TAG, "STARTUP_TIMING: Thumbnail preloading disabled for performance optimization")

                // Initialize MAX SDK in parallel with UI loading for optimal performance
                // SDK initialization runs concurrently but ad loading is still deferred
                initializeMaxSdkInParallel()

                Log.d(TAG, "STARTUP_TIMING: Total async initialization took ${System.currentTimeMillis() - asyncStartTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "Error in async initialization", e)
            }
        }
    }

    override fun onCreateExt() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: onCreateExt() started at $startTime")

        // Essential synchronous setup
        Constant.DEBUG_MODE = false
        admobHelper.setAppOpenAdUnitId("")
        appOpenAdManager.setAdUnitId("ca-app-pub-9844172086883515/5700707131")

        initAdmob()

        // Critical theme initialization (needed for immediate UI)
        val themeStartTime = System.currentTimeMillis()
        ThemeManager.initialize(this)
        AppCompatDelegate.setDefaultNightMode(
            when (ThemeManager.getSelectedTheme()) {
                "AutoTheme" -> AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM
                "BlackTheme", "BlackThemeInverted", "AmoledTheme", "AmoledThemeInverted" -> AppCompatDelegate.MODE_NIGHT_YES
                else -> AppCompatDelegate.MODE_NIGHT_NO
            }
        )
        Log.d(TAG, "STARTUP_TIMING: Theme initialization took ${System.currentTimeMillis() - themeStartTime}ms")

        // Initialize language (needed for immediate UI)
        val languageStartTime = System.currentTimeMillis()
        initializeLanguage()
        Log.d(TAG, "STARTUP_TIMING: Language initialization took ${System.currentTimeMillis() - languageStartTime}ms")

        // Move non-critical initialization to background
        initializeNonCriticalComponents()

        Log.d(TAG, "STARTUP_TIMING: onCreateExt() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Initialize non-critical components asynchronously
     */
    private fun initializeNonCriticalComponents() {
        applicationScope.launch(Dispatchers.IO) {
            try {
                val remoteConfigStartTime = System.currentTimeMillis()
                initRemoteConfig(R.xml.remote_config_defaults) { isSuccess ->
                    onFetchRemoteConfigComplete(isSuccess)
                }
                Log.d(TAG, "STARTUP_TIMING: Async remote config initialization took ${System.currentTimeMillis() - remoteConfigStartTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "Error in non-critical initialization", e)
            }
        }
    }

    private fun onFetchRemoteConfigComplete(isSuccess: Boolean) {
        scheduleAdLoading()
    }

    private fun initializeLanguage() {
        val savedLanguage = appRepository.getLanguage()
        val languageToUse = if (savedLanguage.isNotEmpty()) savedLanguage else appRepository.getDefaultLanguage()
        appRepository.setLocale(this, languageToUse)
    }

    /**
     * Initialize AppLovin MAX SDK asynchronously to avoid blocking main thread during app startup.
     * This includes SDK configuration, initialization, and ad loading setup.
     */
    private suspend fun initializeMaxSdkAsync() {
        val maxStartTime = System.currentTimeMillis()
        val currentThread = Thread.currentThread()
        Log.d(TAG, "MAX_INIT: Starting AppLovin MAX SDK initialization at $maxStartTime on thread: ${currentThread.name} (ID: ${currentThread.id})")

        // Log memory usage before MAX SDK initialization
        val runtime = Runtime.getRuntime()
        val memoryBeforeMax = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024
        Log.d(TAG, "MAX_INIT: Memory usage before MAX SDK init: ${memoryBeforeMax}MB")

        try {
            // Configure AppLovin SDK settings on background thread
            val settingsStartTime = System.currentTimeMillis()
            val settings = AppLovinSdk.getInstance(this).settings
            settings.termsAndPrivacyPolicyFlowSettings.isEnabled = false
            settings.termsAndPrivacyPolicyFlowSettings.privacyPolicyUri =
                "https://ahugames.com/privacy-policy.html".toUri()
            settings.termsAndPrivacyPolicyFlowSettings.termsOfServiceUri = null
            settings.termsAndPrivacyPolicyFlowSettings.setShowTermsAndPrivacyPolicyAlertInGdpr(false)

            val settingsDuration = System.currentTimeMillis() - settingsStartTime
            Log.d(TAG, "MAX_INIT: SDK settings configured in ${settingsDuration}ms")

            // Create initialization configuration
            val configStartTime = System.currentTimeMillis()
            val initConfig = AppLovinSdkInitializationConfiguration.builder("EWVXRT6zy56MNa68ZDiwGnha4SvOOS_z1yZIImOudCJzcu3twVTMBF9TorbmwU2w5pD9qiqO0rBIDPtvmrsRof")
                .setMediationProvider(AppLovinMediationProvider.MAX)
                .build()

            val configDuration = System.currentTimeMillis() - configStartTime
            Log.d(TAG, "MAX_INIT: Initialization configuration created in ${configDuration}ms")

            // Initialize SDK asynchronously with callback
            val sdkInitStartTime = System.currentTimeMillis()
            Log.d(TAG, "MAX_INIT: Starting SDK initialization call at $sdkInitStartTime")

            AppLovinSdk.getInstance(this).initialize(initConfig) { sdkConfig ->
                val callbackReceivedTime = System.currentTimeMillis()
                val sdkInitDuration = callbackReceivedTime - sdkInitStartTime
                Log.d(TAG, "MAX_INIT: SDK initialization callback received after ${sdkInitDuration}ms")

                try {
                    // Handle consent flow configuration
                    val consentStartTime = System.currentTimeMillis()
                    appRepository.setConsentFlowUserGeography(sdkConfig.consentFlowUserGeography == ConsentFlowUserGeography.GDPR)
                    Log.d(TAG, "MAX_INIT: Consent flow configured in ${System.currentTimeMillis() - consentStartTime}ms")

                    // Configure Meta consent if available
                    val metaConsentStartTime = System.currentTimeMillis()
                    val hasMetaConsent = AppLovinPrivacySettings.getAdditionalConsentStatus(89)
                    if (hasMetaConsent != null) {
                        AdSettings.setDataProcessingOptions(arrayOf("LDU"), 1, 1000)
                        Log.d(TAG, "MAX_INIT: Meta consent configured in ${System.currentTimeMillis() - metaConsentStartTime}ms")
                    } else {
                        Log.d(TAG, "MAX_INIT: Meta consent not available, will check after CMP flow")
                    }

                    // Initialize analytics tracker
                    val trackerStartTime = System.currentTimeMillis()
                    initTracker("")
                    Log.d(TAG, "MAX_INIT: Analytics tracker initialized in ${System.currentTimeMillis() - trackerStartTime}ms")

                    // Mark MAX SDK as initialized
                    BatteryApplication.isMaxSdkInitialized = true

                    // Schedule ad loading with delays to avoid blocking UI
                    val adLoadingStartTime = System.currentTimeMillis()
                    scheduleAdLoading()
                    Log.d(TAG, "MAX_INIT: Ad loading scheduled in ${System.currentTimeMillis() - adLoadingStartTime}ms")

                    val totalMaxDuration = System.currentTimeMillis() - maxStartTime
                    Log.d(TAG, "MAX_INIT: AppLovin MAX SDK initialization completed in ${totalMaxDuration}ms")

                    // Log memory usage after MAX SDK initialization
                    val memoryAfterMax = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024
                    val memoryIncrease = memoryAfterMax - memoryBeforeMax
                    Log.d(TAG, "MAX_INIT: Memory usage after MAX SDK init: ${memoryAfterMax}MB (increased by ${memoryIncrease}MB)")

                } catch (e: Exception) {
                    Log.e(TAG, "MAX_INIT: Error in SDK initialization callback", e)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "MAX_INIT: Error during MAX SDK initialization", e)
        }
    }

    /**
     * Schedule ad loading with optimized timing for parallel initialization.
     * Ads load in background while UI is being prepared.
     */
    private fun scheduleAdLoading() {
        if (!BatteryApplication.isMaxSdkInitialized) return

        // App Open Ad - load immediately if enabled (parallel with UI)
        applovinNativeAdManager.loadNativeAd(
            onAdLoaded = { ad ->
            },
            onAdLoadFailed = { error ->
            }
        )

        // Rewarded Ad - load early if not loading in view
        val rvLoadDelay = remoteConfigHelper.getLong("rv_load_delay").toInt()
        Log.d(TAG, "Rewarded: Scheduling Rewarded Ad load with delay of ${rvLoadDelay} seconds and load in view: ${remoteConfigHelper.getBoolean("rv_load_in_view")}")
        Handler(Looper.getMainLooper()).postDelayed({
            val rvLoadInView = remoteConfigHelper.getBoolean("rv_load_in_view")
            if (!rvLoadInView) {
                applovinRewardedAdManager.loadRewardedAd()
            }
        }, rvLoadDelay * 1000L)

        if (!premiumCache.isPremium()) {
            appOpenAdManager.loadAd(this)
        }
    }

    /**
     * Initialize MAX SDK in parallel with UI loading for optimal startup performance.
     * SDK initialization happens immediately but ad loading is deferred until UI is ready.
     */
    private fun initializeMaxSdkInParallel() {
        if (!isMaxSdkInitialized) {
            Log.d(TAG, "STARTUP_TIMING: Starting parallel MAX SDK initialization")
            applicationScope.launch(Dispatchers.IO) {
                initializeMaxSdkAsync()
            }
        } else {
            Log.d(TAG, "MAX_INIT: MAX SDK already initialized, skipping")
        }
    }

    /**
     * Initialize MAX SDK when UI is ready. This method can be called from MainActivity
     * to ensure ads don't block the initial app startup.
     */
    fun initializeMaxSdkWhenReady() {
        if (!isMaxSdkInitialized) {
            Log.d(TAG, "STARTUP_TIMING: Starting deferred MAX SDK initialization from UI")
            applicationScope.launch(Dispatchers.IO) {
                initializeMaxSdkAsync()
            }
        } else {
            Log.d(TAG, "MAX_INIT: MAX SDK already initialized, skipping")
        }
    }

    override fun onActivityPreCreated(activity: Activity, savedInstanceState: Bundle?) {
        super.onActivityPreCreated(activity, savedInstanceState)
        ThemeManager.applyTheme(activity)
    }

    override fun onActivityResumed(activity: Activity) {
        super.onActivityResumed(activity)
        IronSource.onResume(activity)
    }

    override fun onActivityPaused(activity: Activity) {
        super.onActivityPaused(activity)
        IronSource.onPause(activity)
    }

    /**
     * DEPRECATED: Legacy BatteryStatusService - kept for reference only
     * This service has been replaced by CoreBatteryStatsService for unified battery monitoring
     * Multiple battery services were causing resource waste and data inconsistency
     */
    private fun startBatteryStatusService() {
        Log.d(TAG, "DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead")
        // Legacy service startup commented out to eliminate duplicate battery monitoring
        // batteryStatusServiceHelper.startService()
    }

    /**
     * Starts the CoreBatteryStatsService to provide core battery monitoring.
     * Includes comprehensive permission checking and error handling.
     */
    private fun startCoreBatteryStatsService() {
        Log.d(TAG, "Starting CoreBatteryStatsService from Application")

        // Log current permission status for debugging
        ForegroundServiceUtils.logPermissionStatus(this)

        try {
            // Check if we have required permissions
            if (!ForegroundServiceUtils.hasRequiredPermissions(this)) {
                Log.w(TAG, "Missing required permissions for foreground service, starting in fallback mode")
            }

            coreBatteryServiceHelper.startService()

            // Log service status after startup attempt
            val serviceStatus = coreBatteryServiceHelper.getServiceStatus()
            Log.d(TAG, "CoreBatteryStatsService startup status: $serviceStatus")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start CoreBatteryStatsService", e)

            // Log additional context for debugging
            val permissionStatus = ForegroundServiceUtils.getPermissionStatus(this)
            Log.e(TAG, "Permission status during failure: $permissionStatus")
        }
    }

    /**
     * Starts the ChargingOverlayService for charging animation display
     * Only starts if configuration requirements are met (overlay enabled, trial valid, etc.)
     */
    private fun startChargingOverlayService() {
        BatteryLogger.d(TAG, "Starting ChargingOverlayService from Application")
        try {
            chargingOverlayServiceHelper.startServiceIfNeeded()
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to start ChargingOverlayService", e)
        }
    }

    override fun showAOA() {
        if (currentActivity != null) {
            appOpenAdManager.showAdIfAvailable(
                currentActivity!!,
                object : AppOpenAdManager.OnShowAdCompleteListener {
                    override fun onShowAdComplete() {}
                }
            )
        }
    }

    override fun onMoveToForeground() {
        if (!premiumCache.isPremium())
            showAOA()

        Log.d(TAG, "App moved to foreground")

        if (!coreBatteryServiceHelper.isServiceRunning()) {
            Log.d(TAG, "CoreBatteryStatsService not running, starting it now")
            try {
                coreBatteryServiceHelper.startService()

                // Log service status after foreground startup attempt
                val serviceStatus = coreBatteryServiceHelper.getServiceStatus()
                Log.d(TAG, "CoreBatteryStatsService foreground startup status: $serviceStatus")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to restart CoreBatteryStatsService in foreground", e)
            }
        }

        // Check and start ChargingOverlayService if needed and configuration allows
        if (!chargingOverlayServiceHelper.isServiceRunning() && chargingOverlayServiceHelper.shouldStartService()) {
            BatteryLogger.d(TAG, "ChargingOverlayService not running but should be, starting it now")
            chargingOverlayServiceHelper.startServiceIfNeeded()
        }
    }
}