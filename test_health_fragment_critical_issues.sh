#!/bin/bash

# Simple test to check fallback UI when native ad fails to load

set -e

APP_PACKAGE="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
DEVICE_ID="adb-6581d82-M1kKH6._adb-tls-connect._tcp"
ADB_CMD="adb -s $DEVICE_ID"
LOG_FILE="health_test_logs.txt"
LOGCAT_TAGS="NativeAdManager:D"

# Function to print logs with timestamps
log_with_timestamp() {
    echo "[$(date '+%H:%M:%S')] $1"
}

# Check if device is connected
check_device() {
    if ! $ADB_CMD devices | grep -q "$DEVICE_ID.*device$"; then
        echo "ERROR: Device $DEVICE_ID not connected or unauthorized"
        adb devices
        exit 1
    fi
    log_with_timestamp "✅ Device connected: $DEVICE_ID"
}

# Start capturing logcat output
start_logcat() {
    log_with_timestamp "📡 Starting logcat monitoring for NativeAd logs..."
    $ADB_CMD logcat -c
    $ADB_CMD logcat $LOGCAT_TAGS > "$LOG_FILE" &
    LOGCAT_PID=$!
}

# Stop logcat
stop_logcat() {
    if [ ! -z "$LOGCAT_PID" ]; then
        kill $LOGCAT_PID 2>/dev/null || true
        log_with_timestamp "🛑 Logcat monitoring stopped"
    fi
}

# Restart the app to simulate ad loading
reset_app() {
    log_with_timestamp "🔄 Restarting app to trigger ad load..."
    $ADB_CMD shell am force-stop "$APP_PACKAGE"
    sleep 1
    $ADB_CMD shell am start -n "$APP_PACKAGE/.MainActivity"
    sleep 5
}

# Test native ad fallback
test_native_ad_fail_fallback() {
    log_with_timestamp "⏳ Waiting for native ad to fail (and fallback to appear)..."
    sleep 10

    echo ""
    echo "=== 🧪 NATIVE AD FAILURE / FALLBACK LOG ==="
    grep -i "onNativeAdLoadFailed\|native_ad_fallback_shown" "$LOG_FILE" | tail -10
    echo ""

    if grep -q "native_ad_fallback_shown" "$LOG_FILE"; then
        echo "✅ TEST PASSED: Fallback was shown when ad failed"
    else
        echo "❌ TEST FAILED: No fallback detected for native ad failure"
    fi
}

# Cleanup logcat and reset battery if needed
cleanup() {
    stop_logcat
}

# Trap cleanup when script exits
trap cleanup EXIT

# Main function
main() {
    check_device
    start_logcat
    reset_app
    test_native_ad_fail_fallback
}

# Execute
main
