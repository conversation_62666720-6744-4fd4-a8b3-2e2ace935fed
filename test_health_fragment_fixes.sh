#!/bin/bash

# Simple test to check fallback UI when native ad fails to load

set -e

APP_PACKAGE="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
DEVICE_ID="10AC9C1MHS00105"
ADB_CMD="adb -s $DEVICE_ID"
LOG_FILE="health_test_logs.txt"
LOGCAT_TAGS="NativeAdManager:D"

log_with_timestamp() {
    echo "[$(date '+%H:%M:%S')] $1"
}

check_device() {
    if ! $ADB_CMD devices | grep -q "$DEVICE_ID.*device$"; then
        echo "ERROR: Device $DEVICE_ID not connected or unauthorized"
        adb devices
        exit 1
    fi
    log_with_timestamp "✅ Device connected: $DEVICE_ID"
}

start_logcat() {
    log_with_timestamp "📡 Starting logcat monitoring for NativeAd logs..."
    $ADB_CMD logcat -c
    $ADB_CMD logcat $LOGCAT_TAGS > $LOG_FILE &
    LOGCAT_PID=$!
}

stop_logcat() {
    if [ ! -z "$LOGCAT_PID" ]; then
        kill $LOGCAT_PID 2>/dev/null || true
        log_with_timestamp "🛑 Logcat monitoring stopped"
    fi
}

reset_app() {
    log_with_timestamp "🔄 Restarting app to trigger ad load..."
    $ADB_CMD shell am force-stop $APP_PACKAGE
    sleep 1
    $ADB_CMD shell am start -n "$APP_PACKAGE/.MainActivity"
    sleep 5
}

test_native_ad_fail_fallback() {
    log_with_timestamp "⏳ Waiting for native ad to fail (and fallback to appear)..."
    sleep 10

    echo ""
    echo "=== 🧪 NATIVE AD FAILURE / FALLBACK LOG ==="
    grep -i "onNativeAdLoadFailed\|native_ad_fallback_shown" $LOG_FILE | tail -10
    echo ""

    if grep -q "native_ad_fallback_shown" $LOG_FILE; then
        echo "✅ TEST PASSED: Fallback was shown when ad failed"
    else
        echo "❌ TEST FAILED: No fallback detected for native ad failure"
    fi
}

cleanup() {
    stop_logcat
}

trap cleanup EXIT

# Run test
check_device
start_logcat
reset_app
test_native_ad_fail_fallback
