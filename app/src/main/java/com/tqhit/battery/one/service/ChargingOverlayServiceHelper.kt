package com.tqhit.battery.one.service

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat
import com.tqhit.battery.one.repository.AnimationRepository
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for managing the ChargingOverlayService.
 * Provides centralized control for starting and stopping the service,
 * checking its running status, and handling configuration requirements.
 * Follows the established stats module architecture pattern.
 */
@Singleton
class ChargingOverlayServiceHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val appRepository: AppRepository,
    private val animationRepository: AnimationRepository
) {
    
    companion object {
        private const val TAG = "ChargingOverlayServiceHelper"
        private const val OVERLAY_TAG = "ChargingOverlay_Startup"
    }
    
    /**
     * Checks if the ChargingOverlayService is currently running.
     *
     * @return true if the service is active, false otherwise
     */
    fun isServiceRunning(): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        
        @Suppress("DEPRECATION")
        val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
        
        val isRunning = runningServices.any { serviceInfo ->
            serviceInfo.service.className == ChargingOverlayService::class.java.name
        }
        
        BatteryLogger.d(TAG, "ChargingOverlayService running status: $isRunning")
        return isRunning
    }
    
    /**
     * Checks if the service should be started based on configuration settings.
     * Validates animation overlay enabled status and trial expiration.
     *
     * @return true if service should be started, false otherwise
     */
    fun shouldStartService(): Boolean {
        val isOverlayEnabled = appRepository.isAnimationOverlayEnabled()
        val hasVideoPath = !appRepository.getVideoPath().isNullOrEmpty()
        
        BatteryLogger.d(OVERLAY_TAG, "Service startup check - Overlay enabled: $isOverlayEnabled, Has video: $hasVideoPath")
        
        return isOverlayEnabled && hasVideoPath
    }
    
    /**
     * Starts the ChargingOverlayService with proper configuration checks.
     * Handles foreground service start for Android O+ and logs the action.
     * Only starts if configuration requirements are met.
     */
    fun startService() {
        BatteryLogger.d(TAG, "Attempting to start ChargingOverlayService")
        
        // Check configuration requirements before starting
        if (!shouldStartService()) {
            BatteryLogger.d(OVERLAY_TAG, "ChargingOverlayService startup skipped - configuration requirements not met")
            return
        }
        
        // Check if already running to avoid duplicate starts
        if (isServiceRunning()) {
            BatteryLogger.d(TAG, "ChargingOverlayService already running, skipping start")
            return
        }
        
        val intent = Intent(context, ChargingOverlayService::class.java)
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Start as foreground service for Android O+
                BatteryLogger.d(OVERLAY_TAG, "Attempting to start ChargingOverlayService as foreground service")
                ContextCompat.startForegroundService(context, intent)
                BatteryLogger.d(TAG, "ChargingOverlayService started as foreground service successfully")
            } else {
                // Start as regular service for older versions
                context.startService(intent)
                BatteryLogger.d(TAG, "ChargingOverlayService started as regular service")
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error starting ChargingOverlayService", e)
            handleChargingOverlayServiceStartupFailure(e, intent)
        }
    }

    /**
     * Handles ChargingOverlayService startup failures with appropriate fallback mechanisms.
     * Uses safe compatibility approach for Android 12+ ForegroundServiceStartNotAllowedException.
     */
    private fun handleChargingOverlayServiceStartupFailure(exception: Exception, intent: Intent) {
        val isForegroundServiceException = isForegroundServiceStartNotAllowedException(exception)

        when {
            isForegroundServiceException -> {
                BatteryLogger.w(TAG, "ForegroundServiceStartNotAllowedException: Android 12+ background restrictions")
                BatteryLogger.w(OVERLAY_TAG, "Cannot start ChargingOverlayService as foreground service from background")
                BatteryLogger.d(TAG, "Exception class: ${exception.javaClass.simpleName}, API level: ${Build.VERSION.SDK_INT}")
                attemptChargingOverlayFallback(intent, "Android 12+ background restrictions")
            }
            else -> {
                BatteryLogger.e(TAG, "Failed to start ChargingOverlayService", exception)
                attemptChargingOverlayFallback(intent, "Unknown error: ${exception.message}")
            }
        }
    }

    /**
     * Safely checks if an exception is ForegroundServiceStartNotAllowedException without
     * causing ClassNotFoundException on older Android versions.
     */
    private fun isForegroundServiceStartNotAllowedException(exception: Exception): Boolean {
        return when {
            // Check by class name to avoid direct class reference on older Android versions
            exception.javaClass.simpleName == "ForegroundServiceStartNotAllowedException" -> {
                BatteryLogger.d(TAG, "Detected ForegroundServiceStartNotAllowedException by class name")
                true
            }
            // Fallback to string matching for additional safety
            exception.message?.contains("ForegroundServiceStartNotAllowedException") == true -> {
                BatteryLogger.d(TAG, "Detected ForegroundServiceStartNotAllowedException by message content")
                true
            }
            // Check for related background service restriction messages
            exception.message?.contains("startForegroundService() not allowed") == true -> {
                BatteryLogger.d(TAG, "Detected foreground service restriction by message pattern")
                true
            }
            else -> false
        }
    }

    /**
     * Attempts to start ChargingOverlayService using fallback mechanisms.
     */
    private fun attemptChargingOverlayFallback(intent: Intent, reason: String) {
        BatteryLogger.d(OVERLAY_TAG, "Attempting ChargingOverlayService fallback startup due to: $reason")

        try {
            // Try to start as regular service (fallback)
            context.startService(intent)
            BatteryLogger.d(OVERLAY_TAG, "ChargingOverlayService started as regular service (fallback mode)")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to start ChargingOverlayService even in fallback mode", e)
            BatteryLogger.w(OVERLAY_TAG, "ChargingOverlayService startup completely failed - overlay functionality disabled")
        }
    }

    /**
     * Stops the ChargingOverlayService.
     * Logs the action for debugging purposes.
     */
    fun stopService() {
        BatteryLogger.d(TAG, "Stopping ChargingOverlayService")
        
        val intent = Intent(context, ChargingOverlayService::class.java)
        
        try {
            context.stopService(intent)
            BatteryLogger.d(TAG, "ChargingOverlayService stop command sent")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to stop ChargingOverlayService", e)
        }
    }
    
    /**
     * Restarts the ChargingOverlayService by stopping and then starting it.
     * Useful for applying configuration changes or recovering from errors.
     */
    fun restartService() {
        BatteryLogger.d(TAG, "Restarting ChargingOverlayService")
        stopService()
        
        // Small delay to ensure service stops before restarting
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            startService()
        }, 1000)
    }
    
    /**
     * Starts the service only if it's not already running and configuration allows it.
     * This is the recommended method for automatic startup scenarios.
     */
    fun startServiceIfNeeded() {
        BatteryLogger.d(OVERLAY_TAG, "Checking if ChargingOverlayService startup is needed")
        
        if (!isServiceRunning() && shouldStartService()) {
            BatteryLogger.d(OVERLAY_TAG, "Starting ChargingOverlayService - not running and configuration allows")
            startService()
        } else {
            val reason = when {
                isServiceRunning() -> "already running"
                !shouldStartService() -> "configuration requirements not met"
                else -> "unknown reason"
            }
            BatteryLogger.d(OVERLAY_TAG, "ChargingOverlayService startup skipped - $reason")
        }
    }
}
