<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/grey">

    <androidx.media3.ui.PlayerView
        android:id="@+id/playerView"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:background="?attr/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>


    <!-- Date and Time at the top center -->
    <LinearLayout
        android:id="@+id/date_time_container"
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/_80sdp">

        <TextView
            android:id="@+id/text_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="19:00"
            android:textSize="@dimen/_36ssp"
            android:textColor="@android:color/white"
            android:textStyle="bold"/>

        <TextView
            android:id="@+id/text_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Fri, 13 May"
            android:textSize="@dimen/_16ssp"
            android:textColor="@android:color/white"/>
    </LinearLayout>

    <!-- Battery Percentage below the rocket -->
    <TextView
        android:id="@+id/battery_percent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="99%"
        android:textSize="28sp"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="20dp"/>


    <RelativeLayout
        android:layout_width="@dimen/_50sdp"
        android:layout_height="@dimen/_25sdp"
        android:layout_marginTop="@dimen/_16sdp"
        android:layout_marginStart="@dimen/_16sdp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent">
        <Button
            android:id="@+id/back_button"
            android:background="@drawable/grey_block"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginStart="5dp"
            style="@style/Widget.AppCompat.Button.Borderless"/>
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/ic_back"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_12sdp"
            android:layout_marginHorizontal="@dimen/_16sdp"
            android:layout_centerInParent="true"
            android:layout_alignStart="@+id/back_button"
            android:layout_alignEnd="@+id/back_button"/>
    </RelativeLayout>


    <TextView
        android:textSize="@dimen/_14ssp"
        android:textColor="?attr/grey"
        android:id="@+id/time_remaining"
        android:textStyle="bold"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/time_remaining_value"
        android:text="@string/time_remaining"/>
    <TextView
        android:textSize="@dimen/_14ssp"
        android:textColor="?attr/grey"
        android:id="@+id/time_remaining_value"
        android:textStyle="bold"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/overlay_permission_banner"
        android:text="12:23:34s"/>

    <!-- Overlay Permission Banner -->
    <LinearLayout
        android:id="@+id/overlay_permission_banner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16sdp"
        android:layout_marginBottom="@dimen/_8sdp"
        android:background="?attr/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/_12sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/apply_block"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="@dimen/_20sdp"
            android:layout_height="@dimen/_20sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:src="@drawable/ic_ad"
            android:tint="?attr/grey"
            android:background="@drawable/dark_border"
            android:padding="2dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/overlay_permission_banner_title"
                android:textColor="#000000"
                android:textSize="@dimen/_12ssp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_2sdp"
                android:text="@string/overlay_permission_banner_message"
                android:textColor="#000000"
                android:textSize="@dimen/_10ssp" />
        </LinearLayout>
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/apply_block"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_16sdp"
        android:layout_marginHorizontal="@dimen/_16sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
        <Button
            android:id="@+id/applyButton"
            android:background="@drawable/colorr_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            style="@style/Widget.AppCompat.Button.Borderless"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_alignParentBottom="true">
            <TextView
                android:textSize="@dimen/_14ssp"
                android:textColor="?attr/grey"
                android:id="@+id/text_btn"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="12dp"
                android:text="@string/apply"/>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>