package com.tqhit.battery.one.activity.tutorial


import android.content.Intent
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.activity.onboarding.OnboardingFinalActivity
import com.tqhit.battery.one.activity.onboarding.OnboardingSuccessActivity
import com.tqhit.battery.one.base.LocaleAwareActivity
import com.tqhit.battery.one.databinding.ActivityTutorialBinding
import com.tqhit.battery.one.utils.OverlayPermissionUtils
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class TutorialActivity : LocaleAwareActivity<ActivityTutorialBinding>() {

    override val binding by lazy { ActivityTutorialBinding.inflate(layoutInflater) }

    private var overlayPermissionLauncher: ActivityResultLauncher<Intent>? = null

    private val mediaUrl: String by lazy {
        intent.getStringExtra("media_url").toString()
    }

    private val fromMain: Boolean by lazy {
        intent.getBooleanExtra("from_main", false)
    }

    override fun setupData() {
        super.setupData()
        overlayPermissionLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (!android.provider.Settings.canDrawOverlays(this)) {
                Toast.makeText(this, getString(R.string.overlay_permission_denied), Toast.LENGTH_SHORT).show()
                return@registerForActivityResult
            }
            if (OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
                appRepository.setAnimationOverlayEnabled(true)
                navigateToAskEnable()
            } else {
                Toast.makeText(this, "Permission not granted", Toast.LENGTH_SHORT).show()
            }
        }

        // prevent back press
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {}
        }
        onBackPressedDispatcher.addCallback(this, callback)
    }

    override fun setupListener() {
        super.setupListener()

        binding.primaryAction.setOnClickListener{
            overlayPermission()
        }
        binding.secondaryAction.setOnClickListener{
            navigateToFinalScreen()
        }
    }

    private fun overlayPermission() {
            if (!OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
                val intent = OverlayPermissionUtils.createOverlayPermissionIntent(this)
                overlayPermissionLauncher!!.launch(intent)
            } else {
                appRepository.setAnimationOverlayEnabled(true)
                navigateToAskEnable()
            }
        }

    override fun onResume() {
        super.onResume()
        if (OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
            appRepository.setAnimationOverlayEnabled(true)
            navigateToAskEnable()
        }
    }

    private fun navigateToFinalScreen(){
        if (fromMain) {
            finish()
            return
        }
        startActivity(Intent(this, OnboardingFinalActivity::class.java))
        finish()
    }

    private fun navigateToAskEnable() {
        if (fromMain) {
            finish()
            return
        }
        val intent = Intent(this, OnboardingSuccessActivity::class.java)
        intent.putExtra("media_url", mediaUrl)
        startActivity(intent)
        finish()
    }
}
