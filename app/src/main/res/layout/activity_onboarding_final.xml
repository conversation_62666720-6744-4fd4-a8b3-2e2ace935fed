<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/grey">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/nativeAd"
            android:orientation="vertical"
            android:gravity="center">
            <TextView
                android:id="@+id/title_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/more_fun_inside"
                android:layout_marginTop="@dimen/_20sdp"
                android:textColor="?attr/black"
                android:textSize="@dimen/_20ssp"
                android:textStyle="bold"
                android:layout_gravity="center"/>
            <TextView
                android:id="@+id/description_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_10sdp"
                android:text="@string/explore_animations_thief_and_monitor"
                android:layout_marginTop="@dimen/_10sdp"
                android:textColor="?attr/black"
                android:textSize="@dimen/_12ssp"
                android:textAlignment="center"
                android:layout_gravity="center"/>
            <LinearLayout
                android:id="@+id/animation_block"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_50sdp"
                android:layout_marginTop="@dimen/_20sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:background="@drawable/white_block"
                android:orientation="horizontal"
                android:layout_gravity="center_vertical">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/animation"
                    android:layout_marginHorizontal="@dimen/_20sdp"
                    android:textColor="?attr/black"
                    android:textSize="@dimen/_14ssp"
                    android:layout_gravity="center"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/free_text"
                    android:layout_marginHorizontal="@dimen/_2sdp"
                    android:textColor="?attr/black"
                    android:textSize="@dimen/_12ssp"
                    android:layout_gravity="center"/>
                <ImageView
                    android:layout_width="@dimen/_12sdp"
                    android:layout_height="@dimen/_12sdp"
                    android:layout_marginEnd="@dimen/_20sdp"
                    android:contentDescription="@string/free_text"
                    android:scaleX="-1"
                    android:src="@drawable/ic_right_arrow"
                    android:layout_gravity="center_vertical"/>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/monitor_block"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_50sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:background="@drawable/white_block"
                android:orientation="horizontal"
                android:layout_gravity="center_vertical">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/anti_thief"
                    android:layout_marginHorizontal="@dimen/_20sdp"
                    android:textColor="?attr/black"
                    android:textSize="@dimen/_14ssp"
                    android:layout_gravity="center"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/free_text"
                    android:layout_marginHorizontal="@dimen/_2sdp"
                    android:textColor="?attr/black"
                    android:textSize="@dimen/_12ssp"
                    android:layout_gravity="center"/>
                <ImageView
                    android:layout_width="@dimen/_12sdp"
                    android:layout_height="@dimen/_12sdp"
                    android:layout_marginEnd="@dimen/_20sdp"
                    android:contentDescription="@string/free_text"
                    android:scaleX="-1"
                    android:src="@drawable/ic_right_arrow"
                    android:layout_gravity="center_vertical"/>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/thief_block"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_50sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:background="@drawable/white_block"
                android:orientation="horizontal"
                android:layout_gravity="center_vertical">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/battery_monitor"
                    android:layout_marginHorizontal="@dimen/_20sdp"
                    android:textColor="?attr/black"
                    android:textSize="@dimen/_14ssp"
                    android:layout_gravity="center"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/free_text"
                    android:layout_marginHorizontal="@dimen/_2sdp"
                    android:textColor="?attr/black"
                    android:textSize="@dimen/_12ssp"
                    android:layout_gravity="center"/>
                <ImageView
                    android:layout_width="@dimen/_12sdp"
                    android:layout_height="@dimen/_12sdp"
                    android:layout_marginEnd="@dimen/_20sdp"
                    android:contentDescription="@string/free_text"
                    android:scaleX="-1"
                    android:src="@drawable/ic_right_arrow"
                    android:layout_gravity="center_vertical"/>
            </LinearLayout>
            <TextView
                android:id="@+id/tutorial_charging"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_10sdp"
                android:text="@string/tips_connect_your_charger_to_see_the_animation"
                android:layout_marginTop="@dimen/_15sdp"
                android:textColor="?attr/black"
                android:visibility="gone"
                android:textSize="@dimen/_12ssp"
                android:textAlignment="center"
                android:layout_gravity="center"/>
            <TextView
                android:id="@+id/tutorial_toggle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_10sdp"
                android:text="@string/tips_toggle_your_animation_on_the_main_screen"
                android:layout_marginTop="@dimen/_15sdp"
                android:textColor="?attr/black"
                android:visibility="gone"
                android:textSize="@dimen/_12ssp"
                android:textAlignment="center"
                android:layout_gravity="center"/>
        </LinearLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/nativeAd"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/grey" >
    </com.facebook.shimmer.ShimmerFrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
