package com.tqhit.battery.one.dialog.permission

import android.content.Context
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.databinding.DialogAskPermissionBinding
import dagger.hilt.android.qualifiers.ActivityContext

class AskPermissionDialog(
    @ActivityContext private val context: Context,
    private val onConfirm: () -> Unit = {},
    private val onGuide: () -> Unit = {},
    private val onDismiss: () -> Unit = {}
) : AdLibBaseDialog<DialogAskPermissionBinding>(context) {

    companion object {
        private const val TAG = "AskPermissionDialog"
    }

    override val binding by lazy { DialogAskPermissionBinding.inflate(layoutInflater) }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams
    }

    override fun setupListener() {
        super.setupListener()

        binding.primaryAction.setOnClickListener {
            dismiss()
            onConfirm()
        }

        binding.secondaryAction.setOnClickListener {
            dismiss()
            onGuide()
        }

        binding.exitDialog.setOnClickListener {
            dismiss()
            onDismiss()
        }
    }
}