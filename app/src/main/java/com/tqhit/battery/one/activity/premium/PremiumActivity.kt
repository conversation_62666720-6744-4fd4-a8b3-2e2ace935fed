package com.tqhit.battery.one.activity.premium

import android.content.Intent
import android.graphics.Paint
import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager
import com.tqhit.battery.one.databinding.ActivityPremiumBinding
import com.tqhit.battery.one.features.stats.premium.cache.PremiumCache
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import javax.inject.Inject

@AndroidEntryPoint
class PremiumActivity : AdLibBaseActivity<ActivityPremiumBinding>() {

    override val binding by lazy { ActivityPremiumBinding.inflate(layoutInflater) }

    @Inject
    lateinit var rewardedManager: ApplovinRewardedAdManager

    @Inject
    lateinit var premiumCache: PremiumCache

    private val maxPremiumAds = 2
    private var watchedAds = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding.tvPrice.paintFlags = binding.tvPrice.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG

        rewardedManager.loadRewardedAd()
        binding.btnWatch.setOnClickListener { showRewardAd() }

        updateUI()
    }

    override fun setupUI() {
        super.setupUI()
        updateUI()
    }

    private fun updateUI() {
        val isPremium = premiumCache.isPremium()
        val watched = premiumCache.getWatchedAdsCount()

        if (isPremium) {
            val (hours, minutes) = premiumCache.getRemainingPremiumTime()

            binding.tvTitle.text = getString(
                R.string.your_premium_will_expire_in_d_hours_m_minutes,
                hours,
                minutes
            )

            binding.tvContent1.visibility = View.GONE
            binding.tvContent2.visibility = View.GONE
            binding.tvPrice.visibility = View.GONE
            binding.countAds.visibility = View.GONE
            binding.btnWatch.text = getString(R.string.you_are_a_premium_user)
            binding.btnWatch.background = null
            binding.btnWatch.setTextColor(getThemeColor(this, R.attr.black))
        } else {
            binding.btnWatch.text = getString(R.string.watch_ads)
            binding.btnWatch.isEnabled = watched < maxPremiumAds
            binding.btnWatch.background = ContextCompat.getDrawable(this, R.drawable.green_button)
            binding.btnWatch.setTextColor(ContextCompat.getColor(this, R.color.white))

            binding.tvContent1.visibility = View.VISIBLE
            binding.tvContent2.visibility = View.VISIBLE
            binding.tvPrice.visibility = View.VISIBLE
            binding.countAds.visibility = View.VISIBLE

            binding.countAds.text = getString(R.string.watched_format, watched, maxPremiumAds)
        }
    }

    private fun getThemeColor(context: android.content.Context, attr: Int): Int {
        val typedValue = android.util.TypedValue()
        val theme = context.theme
        theme.resolveAttribute(attr, typedValue, true)
        return typedValue.data
    }

    private fun showRewardAd() {
        rewardedManager.showRewardedAd(this) {
            val currentCount = premiumCache.getWatchedAdsCount()
            val newCount = currentCount + 1
            premiumCache.setWatchedAdsCount(newCount)

            if (newCount >= maxPremiumAds) {
                lifecycleScope.launch {
                    premiumCache.savePremiumActivationTime()
                    premiumCache.setWatchedAdsCount(0)
                    Toast.makeText(
                        this@PremiumActivity,
                        getString(R.string.you_are_a_premium_user),
                        Toast.LENGTH_SHORT
                    ).show()
                    updateUI()
                }
            } else {
                updateUI()
            }
        }
    }

    override fun setupListener() {
        super.setupListener()

        binding.btnBack.setOnClickListener {
            val intent = Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
            startActivity(intent)
            finish()
        }

    }

}
