<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/grey">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/nativeAd"
        android:orientation="vertical"
        android:gravity="center">
        <TextView
            android:id="@+id/title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/you_did_it_time_to_play"
            android:layout_marginTop="@dimen/_20sdp"
            android:textColor="?attr/black"
            android:textSize="@dimen/_20ssp"
            android:textStyle="bold"
            android:layout_gravity="center"/>

        <TextView
            android:id="@+id/description_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginHorizontal="@dimen/_20sdp"
            android:text="@string/your_animation_is_ready_turn_on_all_the_magic"
            android:textAlignment="center"
            android:textColor="?attr/black"
            android:textSize="@dimen/_12ssp" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:background="@drawable/white_block">
            <androidx.media3.ui.PlayerView
                android:id="@+id/player_view"
                android:padding="@dimen/_5sdp"
                android:layout_width="@dimen/_120sdp"
                android:layout_height="@dimen/_160sdp"/>

            <LinearLayout
                android:id="@+id/date_time_container"
                android:orientation="vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_centerHorizontal="true"
                android:layout_alignParentTop="true"
                android:layout_marginTop="@dimen/_10sdp">

                <TextView
                    android:id="@+id/text_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="19:00"
                    android:textSize="@dimen/_24ssp"
                    android:textColor="@android:color/white"
                    android:textStyle="bold"/>

                <TextView
                    android:id="@+id/text_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Fri, 13 May"
                    android:textSize="@dimen/_10ssp"
                    android:textColor="@android:color/white"/>
            </LinearLayout>
            <TextView
                android:id="@+id/battery_percent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="99%"
                android:textSize="@dimen/_12ssp"
                android:textColor="@android:color/white"
                android:textStyle="bold"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/_136sdp"/>
        </RelativeLayout>
    </LinearLayout>

    <RelativeLayout
        android:layout_marginBottom="@dimen/_10sdp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/nativeAd">

        <TextView
            android:id="@+id/secondary_action"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_30sdp"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_toStartOf="@id/primary_layout"
            android:paddingHorizontal="@dimen/_10sdp"
            android:text="@string/no"
            android:textColor="?attr/black"
            android:textSize="@dimen/_11ssp"
            android:gravity="center"/>

        <LinearLayout
            android:id="@+id/primary_layout"
            android:layout_width="@dimen/_120sdp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="9dp"
            android:animateLayoutChanges="true"
            android:background="@drawable/white_block"
            android:orientation="vertical">

            <Button
                android:id="@+id/primary_action"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/grey_block"
                android:longClickable="false"
                android:paddingStart="10dp"
                android:paddingTop="12.5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="12.5dp"
                android:text="@string/yes"
                android:textAllCaps="false"
                android:textColor="?attr/black"
                android:textSize="@dimen/_15ssp" />
        </LinearLayout>
    </RelativeLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/nativeAd"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/grey" >
    </com.facebook.shimmer.ShimmerFrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>