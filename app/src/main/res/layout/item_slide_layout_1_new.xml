<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/step_1_2_choose_your_animation"
        android:layout_gravity="center"
        android:textSize="@dimen/_15ssp"
        android:textColor="?attr/black"
        android:layout_marginTop="@dimen/_12sdp"/>

    <!-- N<PERSON><PERSON> dung chính chiếm toàn bộ phần còn lại -->
    <LinearLayout
        android:id="@+id/centerContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="24dp">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieView"
            android:layout_width="400dp"
            android:layout_height="400dp"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/hello_anim" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/next_page"
        android:layout_width="80dp"
        android:layout_height="40dp"
        android:layout_gravity="end"
        android:layout_marginBottom="25dp"
        android:layout_marginEnd="30dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:outlineProvider="background"
        android:stateListAnimator="@null"
        android:visibility="visible">

        <TextView
            android:id="@+id/swipe_text"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_16sdp"
            android:layout_marginEnd="@dimen/_3sdp"
            android:text="@string/next"
            android:textAllCaps="true"
            android:textColor="?attr/black"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="@dimen/_12sdp"
            android:layout_height="@dimen/_12sdp"
            android:contentDescription="@string/next"
            android:scaleX="-1"
            android:src="@drawable/ic_strelka" />
    </LinearLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/nativeAd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/grey" />
</LinearLayout>
