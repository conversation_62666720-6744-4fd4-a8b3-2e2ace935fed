<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout
        android:orientation="vertical"
        android:id="@+id/ad_unit_content"
        android:paddingTop="@dimen/_5sdp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/_5sdp"
            android:layout_marginHorizontal="@dimen/_5sdp">
            <TextView
                android:id="@+id/tv"
                android:backgroundTint="?attr/white"
                style="@style/AppTheme.Ads"/>
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ad_app_icon"
                    android:paddingTop="@dimen/_18sdp"
                    android:layout_width="@dimen/_48sdp"
                    android:layout_height="@dimen/_50sdp"
                    android:src="?attr/white"
                    android:adjustViewBounds="true"
                    android:paddingHorizontal="@dimen/_8sdp"/>
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textSize="@dimen/_10ssp"
                        android:textStyle="bold"
                        android:textColor="?attr/black"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:id="@+id/ad_headline"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:text="Lorem Ipsum"
                        android:maxLines="2"
                        android:lines="2"
                        android:layout_weight="3"/>
                    <TextView
                        android:textSize="@dimen/_8ssp"
                        android:textStyle="bold"
                        android:textColor="?attr/black"
                        android:gravity="bottom"
                        android:id="@+id/ad_advertiser"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:text="Lorem Ipsum"
                        android:lines="1"
                        android:layout_weight="2"
                        android:paddingVertical="@dimen/_5sdp"/>
                </LinearLayout>
            </LinearLayout>
            <TextView
                android:textSize="@dimen/_9ssp"
                android:textColor="?attr/black"
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/appbar_scrolling_view_behavior"
                android:maxLines="3"
                android:layout_below="@+id/ll"
                android:paddingHorizontal="@dimen/_8sdp"/>
            <com.google.android.gms.ads.nativead.MediaView
                android:layout_gravity="center"
                android:id="@+id/ad_media"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:scaleType="fitXY"
                android:adjustViewBounds="true"
                android:minWidth="120dp"
                android:minHeight="50dp"
                android:layout_weight="1"
                android:layout_below="@+id/ad_body"
                android:paddingHorizontal="@dimen/_30sdp"/>
            <Button
                android:gravity="center"
                android:textStyle="bold"
                android:id="@+id/ad_call_to_action"
                android:textColor="?attr/black"
                android:background="@drawable/white_block_ads"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_40sdp"
                android:layout_marginTop="@dimen/_5sdp"
                android:text="Install"
                android:layout_below="@+id/ad_media"/>
        </LinearLayout>
    </RelativeLayout>
</com.google.android.gms.ads.nativead.NativeAdView>

