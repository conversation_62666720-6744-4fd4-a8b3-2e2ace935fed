<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_marginStart="@dimen/_3sdp"
        android:id="@+id/goToMain"
        android:layout_width="80dp"
        android:layout_height="30dp"
        android:layout_gravity="end"
        android:gravity="center"
        android:orientation="horizontal"
        android:outlineProvider="background"
        android:stateListAnimator="@null"
        android:visibility="visible">
        <TextView
            android:layout_marginStart="3dp"
            android:gravity="center"
            android:id="@+id/swipe_text"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_16sdp"
            android:text="Skip"
            android:textAllCaps="true"
            android:textColor="?attr/black"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold" />
        <ImageView
            android:layout_marginStart="3dp"
            android:layout_width="@dimen/_15sdp"
            android:layout_height="@dimen/_15sdp"
            android:contentDescription="@string/next"
            android:scaleX="-1"
            android:src="@drawable/ic_strelka" />

    </LinearLayout>
    <!-- Nội dung chính chiếm toàn bộ phần còn lại -->
    <LinearLayout
        android:id="@+id/typeContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

            <TextView
                android:id="@+id/texContent1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Animation Set!"
                android:textSize="22sp"
                android:textColor="?attr/black" />
            <TextView
                android:padding="@dimen/_32sdp"
                android:textAlignment="center"
                android:id="@+id/texContent2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="To show your new animation when charging, please enable Overlay Permission. This allows the animation to appear on top of your screen."
                android:textSize="18sp"
                android:textColor="?attr/black" />





    </LinearLayout>

    <LinearLayout
        android:layout_gravity="bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_marginStart="@dimen/_3sdp"
            android:id="@+id/goToTutorial"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="horizontal"
            android:outlineProvider="background"
            android:stateListAnimator="@null"
            android:visibility="visible">
            <TextView

                android:layout_marginStart="3dp"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_16sdp"
                android:text="I need help"
                android:textAllCaps="true"
                android:textColor="?attr/black"
                android:textSize="@dimen/_9ssp"
                android:textStyle="italic" />

        </LinearLayout>

        <LinearLayout
            android:layout_gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:background="@drawable/white_block"
            android:orientation="vertical">

            <Button
                android:textSize="@dimen/_14ssp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:textColor="?attr/black"
                android:id="@+id/goToSettings"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:longClickable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:text="Go to Overlay Permission Setting"
                android:textAllCaps="false"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                style="@style/Widget.AppCompat.Button.Borderless"/>
        </LinearLayout>
    </LinearLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/nativeAd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/grey" />
</LinearLayout>
