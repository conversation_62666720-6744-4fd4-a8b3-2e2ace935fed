package com.tqhit.battery.one.service

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.content.ContextCompat
import com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity
import com.tqhit.battery.one.repository.AnimationRepository
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.utils.NotificationUtils
import dagger.hilt.android.AndroidEntryPoint
import java.io.File
import javax.inject.Inject

@AndroidEntryPoint
class ChargingOverlayService : Service() {
    @Inject lateinit var appRepository: AppRepository
    @Inject lateinit var animationRepository: AnimationRepository
    private var isCharging = false
    private var videoPath: String? = null
    private var isForegroundServiceActive = false

    private val batteryReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            try {
                val status = intent?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
                val charging = status == BatteryManager.BATTERY_STATUS_CHARGING || status == BatteryManager.BATTERY_STATUS_FULL

                BatteryLogger.d(TAG, "Battery status changed: status=$status, charging=$charging, wasCharging=$isCharging")

                if (charging && !isCharging) {
                    isCharging = true
                    BatteryLogger.d(TAG, "Charging started - attempting to show overlay")
                    startOverlayActivity(context, intent)
                } else if (!charging && isCharging) {
                    isCharging = false
                    BatteryLogger.d(TAG, "Charging stopped")
                }
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "Error processing battery status change", e)
            }
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        BatteryLogger.d(TAG, "ChargingOverlayService onStartCommand called")

        // Create notification channel first
        NotificationUtils.createNotificationChannel(this)

        // Attempt to start as foreground service with proper error handling
        attemptForegroundServiceStart()

        // Register battery receiver
        try {
            registerReceiver(batteryReceiver, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
            BatteryLogger.d(TAG, "Battery receiver registered successfully")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to register battery receiver", e)
        }

        // Check current charging state immediately
        checkCurrentChargingState()

        return START_STICKY
    }

    private fun startOverlayActivity(context: Context, intent: Intent?) {
        BatteryLogger.d(TAG, "Attempting to start charging overlay activity")

        if (!appRepository.isAnimationOverlayEnabled()) {
            BatteryLogger.d(TAG, "Animation overlay disabled - skipping overlay activity")
            return
        }

        videoPath = determineVideoSource()
        if (videoPath.isNullOrEmpty()) {
            BatteryLogger.w(TAG, "No video source available - skipping overlay activity")
            return
        }

        BatteryLogger.d(TAG, "Starting overlay activity with video path: $videoPath")

        try {
            val overlayIntent = Intent(this, ChargingOverlayActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                putExtra("extra_video_path", videoPath)
            }
            context.startActivity(overlayIntent)
            BatteryLogger.d(TAG, "Charging overlay activity started successfully")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to start charging overlay activity", e)
        }
    }

    /**
     * Determines the best video source using the same priority logic as ChargingOverlayActivity
     */
    private fun determineVideoSource(): String? {
        BatteryLogger.d(TAG, "Determining video source for charging overlay")

        // First priority: Check if permanent file exists
        val permanentFilePath = appRepository.getVideoPath()
        if (!permanentFilePath.isNullOrEmpty()) {
            val file = File(permanentFilePath)
            if (file.exists() && file.length() > 0) {
                BatteryLogger.d(TAG, "Using permanent file path: $permanentFilePath (size: ${file.length()} bytes)")
                return permanentFilePath
            } else {
                BatteryLogger.d(TAG, "Permanent file path exists but file is missing or empty: $permanentFilePath")
            }
        } else {
            BatteryLogger.d(TAG, "No permanent file path configured")
        }

        // Second priority: Fall back to applied animation URL for immediate playback
        val appliedAnimationUrl = appRepository.getAppliedAnimationUrl()
        if (!appliedAnimationUrl.isNullOrEmpty()) {
            BatteryLogger.d(TAG, "Using applied animation URL: $appliedAnimationUrl")
            return appliedAnimationUrl
        } else {
            BatteryLogger.d(TAG, "No applied animation URL available")
        }

        BatteryLogger.w(TAG, "No video source available for charging overlay")
        return null
    }

    /**
     * Attempts to start the service as a foreground service with comprehensive error handling.
     * Handles Android 12+ ForegroundServiceStartNotAllowedException gracefully.
     */
    private fun attemptForegroundServiceStart() {
        BatteryLogger.d(TAG, "Attempting to start ChargingOverlayService as foreground service (Android ${Build.VERSION.SDK_INT})")

        try {
            val notification = NotificationUtils.createChargingOverlayNotification(this)
            startForeground(NOTIFICATION_ID, notification)
            isForegroundServiceActive = true
            BatteryLogger.d(TAG, "Successfully started ChargingOverlayService as foreground service")
        } catch (e: Exception) {
            handleForegroundServiceFailure(e)
        }
    }

    /**
     * Handles foreground service startup failures with appropriate logging and fallback.
     * Specifically handles Android 12+ ForegroundServiceStartNotAllowedException.
     */
    private fun handleForegroundServiceFailure(exception: Exception) {
        isForegroundServiceActive = false

        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
            isForegroundServiceStartNotAllowedException(exception) -> {
                BatteryLogger.w(TAG, "ForegroundServiceStartNotAllowedException: Android 12+ background restrictions")
                BatteryLogger.w(TAG, "ChargingOverlayService will continue in fallback mode without foreground notification")
                BatteryLogger.d(TAG, "This is expected when app is started from background context")
            }
            else -> {
                BatteryLogger.e(TAG, "Unexpected error starting foreground service", exception)
                BatteryLogger.w(TAG, "ChargingOverlayService will continue in fallback mode")
            }
        }
    }

    /**
     * Safely checks if an exception is ForegroundServiceStartNotAllowedException without
     * causing ClassNotFoundException on older Android versions.
     */
    private fun isForegroundServiceStartNotAllowedException(exception: Exception): Boolean {
        return when {
            // Check by class name to avoid direct class reference on older Android versions
            exception.javaClass.simpleName == "ForegroundServiceStartNotAllowedException" -> {
                BatteryLogger.d(TAG, "Detected ForegroundServiceStartNotAllowedException by class name")
                true
            }
            // Fallback to string matching for additional safety
            exception.message?.contains("ForegroundServiceStartNotAllowedException") == true -> {
                BatteryLogger.d(TAG, "Detected ForegroundServiceStartNotAllowedException by message content")
                true
            }
            // Check for related background service restriction messages
            exception.message?.contains("startForegroundService() not allowed") == true -> {
                BatteryLogger.d(TAG, "Detected foreground service restriction by message pattern")
                true
            }
            else -> false
        }
    }

    /**
     * Checks the current charging state and updates internal state accordingly.
     */
    private fun checkCurrentChargingState() {
        try {
            val batteryStatus = registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
            val status = batteryStatus?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
            isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING || status == BatteryManager.BATTERY_STATUS_FULL
            BatteryLogger.d(TAG, "Current charging state: $isCharging (status: $status)")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to check current charging state", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        BatteryLogger.d(TAG, "ChargingOverlayService onDestroy called")

        try {
            unregisterReceiver(batteryReceiver)
            BatteryLogger.d(TAG, "Battery receiver unregistered successfully")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error unregistering battery receiver", e)
        }

        // Stop foreground service if active
        if (isForegroundServiceActive) {
            try {
                stopForeground(STOP_FOREGROUND_REMOVE)
                BatteryLogger.d(TAG, "Foreground service stopped successfully")
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "Error stopping foreground service", e)
            }
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    companion object {
        private const val TAG = "ChargingOverlayService"
        private const val NOTIFICATION_ID = 1001
    }
}