package com.tqhit.battery.one.features.stats.discharge.presentation

import android.content.Context
import android.os.Bundle
import android.os.PowerManager
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
import com.tqhit.battery.one.databinding.NewFragmentDischargeBinding
import com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter
import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel
import com.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper
import com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * The main fragment for the discharge screen
 */
@AndroidEntryPoint
class DischargeFragment : Fragment() {
    companion object {
        private const val TAG = "DischargeFragment"
    }

    // SERVICE_INVESTIGATION: Track fragment creation time
    private val fragmentCreationTime = System.currentTimeMillis()

    init {
        Log.d(TAG, "SERVICE_INVESTIGATION: === DISCHARGE FRAGMENT CREATED ===")
        Log.d(TAG, "SERVICE_INVESTIGATION: Fragment instance created at: $fragmentCreationTime")
        Log.d(TAG, "SERVICE_INVESTIGATION: Fragment hashCode: ${hashCode()}")
    }

    private val maxNativeAdView: MaxNativeAdView by lazy {
        createNativeAdView()
    }

    private val viewModel: DischargeViewModel by viewModels()
    private val sharedNavigationViewModel: SharedNavigationViewModel by activityViewModels()
    private var _binding: NewFragmentDischargeBinding? = null
    private val binding get() = _binding!!

    // Helper classes - lazy initialization for better memory usage
    private val uiUpdater: DischargeUiUpdater by lazy {
        DischargeUiUpdater(requireContext(), binding, timeConverter)
    }
    private val animationHelper: AnimationHelper by lazy {
        AnimationHelper(binding, uiUpdater)
    }
    @Inject lateinit var infoButtonManager: InfoButtonManager


    @Inject
    lateinit var applovinNativeAdManager: ApplovinNativeAdManager
    @Inject
    lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    // Services
    private lateinit var powerManager: PowerManager

    // Back navigation
    private lateinit var btnBackNavigation: ImageButton

    @Inject lateinit var timeConverter: TimeConverter
    @Inject lateinit var appLifecycleManager: AppLifecycleManager



    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: DischargeFragment.onCreateView() started at $startTime")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: onCreateView called")
        Log.d(TAG, "DischargeFragment: Creating view - fragment instance: ${this.hashCode()}")
        Log.d(TAG, "Navigation: DischargeFragment onCreateView - preparing UI binding")
        Log.d(TAG, "CoreBatteryStatsService: Fragment view creation initiated")

        _binding = NewFragmentDischargeBinding.inflate(inflater, container, false)
        Log.d(TAG, "DischargeFragment: View binding created successfully")

        Log.d(TAG, "STARTUP_TIMING: DischargeFragment.onCreateView() completed in ${System.currentTimeMillis() - startTime}ms")
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: DischargeFragment.onViewCreated() started at $startTime")

        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "FRAGMENT_LIFECYCLE: onViewCreated called")
        Log.d(TAG, "DischargeFragment: View created - fragment instance: ${this.hashCode()}")
        Log.d(TAG, "Navigation: DischargeFragment onViewCreated - setting up data subscriptions")
        Log.d(TAG, "CoreBatteryStatsService: Fragment ready to receive data updates")
        Log.d(TAG, "MultiNavigation: DischargeFragment onViewCreated - instance=${this.hashCode()}, viewModel=${viewModel.hashCode()}")

        val powerManagerStartTime = System.currentTimeMillis()
        powerManager = requireContext().getSystemService(Context.POWER_SERVICE) as PowerManager
        Log.d(TAG, "STARTUP_TIMING: PowerManager initialization took ${System.currentTimeMillis() - powerManagerStartTime}ms")

        // Initialize critical components immediately
        val observeStartTime = System.currentTimeMillis()
        Log.d(TAG, "DischargeFragment: Starting data subscription setup")
        observeUiState()
        observeNavigationState()
        Log.d(TAG, "STARTUP_TIMING: observeUiState() and observeNavigationState() took ${System.currentTimeMillis() - observeStartTime}ms")
        Log.d(TAG, "CoreBatteryStatsService: Data subscription established")
        Log.d(TAG, "SharedNavigationViewModel: Navigation state observation established")

        // Setup back navigation
        Log.d(TAG, "BACK_NAVIGATION: About to call setupBackNavigation()")
        setupBackNavigation()
        Log.d(TAG, "BACK_NAVIGATION: setupBackNavigation() call completed")

        // Defer non-critical initialization to improve startup performance
        initializeNonCriticalComponentsAsync()

        Log.d(TAG, "STARTUP_TIMING: DischargeFragment.onViewCreated() completed in ${System.currentTimeMillis() - startTime}ms")
        setupNativeAd()
    }

    private fun setupNativeAd(){

        val container = binding.nativeAd

        applovinNativeAdManager.loadNativeAd(
            onAdLoaded = {
                if (!this.isAdded || this.isDetached || this.isRemoving) {
                    return@loadNativeAd
                }
                applovinNativeAdManager.removeCachedNativeAd(it)
                applovinNativeAdManager.render(it, maxNativeAdView)
                container.removeAllViews()
                container.hideShimmer()
                (maxNativeAdView.parent as? ViewGroup)?.removeView(maxNativeAdView)
                container.addView(maxNativeAdView)
            },
            onAdLoadFailed = { errorMsg ->
                Log.e("NativeAd", "Failed to load: $errorMsg")
            }
        )

    }

    private fun createNativeAdView(): MaxNativeAdView
    {
        val binder: MaxNativeAdViewBinder =
            MaxNativeAdViewBinder.Builder(R.layout.layout_native_ads)
                .setTitleTextViewId(R.id.title_text_view)
                .setBodyTextViewId(R.id.body_text_view)
                .setStarRatingContentViewGroupId(R.id.star_rating_view )
                .setAdvertiserTextViewId(R.id.advertiser_text_view)
                .setIconImageViewId(R.id.icon_image_view)
                .setMediaContentViewGroupId(R.id.media_view_container)
                .setOptionsContentViewGroupId(R.id.ad_options_view)
                .setCallToActionButtonId(R.id.cta_button)
                .build()
        return MaxNativeAdView(binder, requireContext())
    }

    override fun onResume() {
        super.onResume()
        val resumeTime = System.currentTimeMillis()

        Log.d(TAG, "SERVICE_INVESTIGATION: === DISCHARGE FRAGMENT RESUME ===")
        Log.d(TAG, "SERVICE_INVESTIGATION: Fragment resumed at: $resumeTime")
        Log.d(TAG, "SERVICE_INVESTIGATION: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached")
        Log.d(TAG, "SERVICE_INVESTIGATION: Activity state - activity: ${activity != null}, context: ${context != null}")

        Log.d(TAG, "FRAGMENT_LIFECYCLE: onResume called - Fragment is now ACTIVE")
        Log.d(TAG, "DischargeFragment: Fragment resumed - instance: ${this.hashCode()}")
        Log.d(TAG, "Navigation: DischargeFragment onResume - checking data persistence")
        Log.d(TAG, "CoreBatteryStatsService: Fragment active - expecting data updates")
        Log.d(TAG, "MultiNavigation: DischargeFragment onResume - instance=${this.hashCode()}, viewModel=${viewModel.hashCode()}")
        appLifecycleManager.setDischargeFragmentActive(true)

        // Notify UI updater about resume
        uiUpdater.onFragmentResumed()

        // SERVICE_CONNECTION_FIX: Validate and recover service connections on resume
        validateAndRecoverServiceConnections()

        // Trigger UI validation on resume to catch any staleness issues
        viewLifecycleOwner.lifecycleScope.launch {
            delay(500) // Small delay to ensure UI is ready
            val currentState = viewModel.uiState.value
            if (_binding != null && !currentState.isLoadingInitial) {
                Log.d(TAG, "FRAGMENT_LIFECYCLE: Validating UI state on resume")
                Log.d(TAG, "FRAGMENT_LIFECYCLE: ${uiUpdater.getStalenessMetrics()}")
                uiUpdater.updateStatusAndEstimates(currentState)
                uiUpdater.updateLossOfCharge(currentState)
                uiUpdater.updateCurrentSessionDetails(currentState)
            }
        }

        Log.d(TAG, "SERVICE_INVESTIGATION: === DISCHARGE FRAGMENT RESUME COMPLETED ===")
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "FRAGMENT_LIFECYCLE: onPause called - Fragment is now INACTIVE")
        Log.d(TAG, "DischargeFragment: Fragment paused - instance: ${this.hashCode()}")
        Log.d(TAG, "Navigation: DischargeFragment onPause - data subscriptions should remain active")
        Log.d(TAG, "CoreBatteryStatsService: Fragment inactive but data flow should continue")
        appLifecycleManager.setDischargeFragmentActive(false)

        // Notify UI updater about pause for background tracking
        uiUpdater.onFragmentPaused()
    }

    override fun onDestroyView() {
        Log.d(TAG, "FRAGMENT_LIFECYCLE: onDestroyView called")
        Log.d(TAG, "DischargeFragment: View destroyed - instance: ${this.hashCode()}")
        Log.d(TAG, "Navigation: DischargeFragment onDestroyView - checking data preservation")
        Log.d(TAG, "CoreBatteryStatsService: Fragment view destroyed - ViewModel should retain data")
        appLifecycleManager.setDischargeFragmentActive(false)
        super.onDestroyView()
        animationHelper.cleanup()
        _binding = null
    }

    /**
     * Sets up back navigation button click handling.
     * Uses fragment back stack management since this fragment is managed by DynamicNavigationManager.
     */
    private fun setupBackNavigation() {
        try {
            Log.d(TAG, "BACK_NAVIGATION: Setting up back navigation button")

            Log.d(TAG, "BACK_NAVIGATION: Attempting to find back navigation button")
            btnBackNavigation = binding.includeBackNavigation.btnBackNavigation
            Log.d(TAG, "BACK_NAVIGATION: Back navigation button found successfully")

            btnBackNavigation.setOnClickListener {
                Log.d(TAG, "BACK_NAVIGATION: Back button clicked - navigating back to Others fragment")
                Log.d(TAG, "Navigation: Using DynamicNavigationManager to preserve fragment lifecycle")
                applovinInterstitialAdManager.showInterstitialAd(
                    "default_iv",
                    requireActivity(),
                ) {
                    try {
                        // FRAGMENT_LIFECYCLE_FIX: Use DynamicNavigationManager instead of direct FragmentManager
                        // This preserves the fragment and ViewModel instead of destroying them
                        val mainActivity = requireActivity() as? com.tqhit.battery.one.activity.main.MainActivity
                        if (mainActivity != null) {
                            Log.d(TAG, "BACK_NAVIGATION: Using MainActivity's navigation system")
                            // Navigate to Others fragment using the main activity's navigation system
                            // This will use DynamicNavigationManager's show/hide pattern
                            mainActivity.navigateToOthersFragment()
                            Log.d(TAG, "BACK_NAVIGATION: Successfully navigated back using DynamicNavigationManager")
                        } else {
                            Log.w(TAG, "BACK_NAVIGATION: MainActivity not available - using back stack fallback")

                            // Fallback: Try back stack first
                            val fragmentManager = requireActivity().supportFragmentManager
                            if (fragmentManager.backStackEntryCount > 0) {
                                fragmentManager.popBackStack()
                                Log.d(TAG, "BACK_NAVIGATION: Successfully navigated back using popBackStack()")
                                fragmentManager.executePendingTransactions()
                            } else {
                                Log.e(TAG, "BACK_NAVIGATION: No back stack entries and MainActivity not available")
                                // Don't use replace() as fallback - this destroys the ViewModel
                                Log.e(TAG, "BACK_NAVIGATION: Cannot navigate back without destroying fragment state")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "BACK_NAVIGATION: Error during navigation", e)
                        Log.e(TAG, "Navigation: Failed to preserve fragment lifecycle during back navigation")

                        // Final fallback: Try back stack only (avoid replace() to preserve ViewModel)
                        try {
                            val fragmentManager = requireActivity().supportFragmentManager
                            if (fragmentManager.backStackEntryCount > 0) {
                                fragmentManager.popBackStack()
                                fragmentManager.executePendingTransactions()
                                Log.d(TAG, "BACK_NAVIGATION: Emergency fallback using popBackStack() successful")
                            } else {
                                Log.e(TAG, "BACK_NAVIGATION: All navigation attempts failed - cannot preserve fragment state")
                            }
                        } catch (fallbackException: Exception) {
                            Log.e(TAG, "BACK_NAVIGATION: All navigation attempts failed", fallbackException)
                        }
                    }
                }
            }

            Log.d(TAG, "BACK_NAVIGATION: Back navigation setup completed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "BACK_NAVIGATION: Error setting up back navigation", e)
        }
    }

    private fun initializeHelpers() {
        // Trigger lazy initialization by accessing the properties
        // This ensures they are created when needed
        uiUpdater.toString() // Trigger lazy init
        animationHelper.toString() // Trigger lazy init

        // Initialize InfoButtonManager with binding
        infoButtonManager.initialize(binding)
    }

    /**
     * Initialize non-critical components asynchronously to improve startup performance
     */
    private fun initializeNonCriticalComponentsAsync() {
        // Use viewLifecycleOwner to ensure proper lifecycle management
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
            try {
                // Check if fragment is still attached before proceeding
                if (!isAdded || _binding == null) {
                    Log.d(TAG, "Fragment not attached, skipping async initialization")
                    return@launch
                }

                // Initialize helpers in background
                val helpersStartTime = System.currentTimeMillis()
                withContext(Dispatchers.Default) {
                    // Prepare helper data in background if needed
                }

                // Check again before UI operations
                if (!isAdded || _binding == null) {
                    Log.d(TAG, "Fragment detached during async initialization")
                    return@launch
                }

                // Initialize helpers on main thread (they need UI access)
                initializeHelpers()
                Log.d(TAG, "STARTUP_TIMING: Async initializeHelpers() took ${System.currentTimeMillis() - helpersStartTime}ms")

                // Set up info button listeners
                val listenersStartTime = System.currentTimeMillis()
                infoButtonManager.setupInfoButtonListeners(
                    resetSessionCallback = { resetSession() },
                    getCurrentSession = { viewModel.uiState.value.currentSession },
                    getBatteryCapacity = { viewModel.uiState.value.batteryCapacityMah }
                )
                Log.d(TAG, "STARTUP_TIMING: Async setupInfoButtonListeners() took ${System.currentTimeMillis() - listenersStartTime}ms")

            } catch (e: Exception) {
                // Only log if it's not a cancellation exception
                if (e !is kotlinx.coroutines.CancellationException) {
                    Log.e(TAG, "Error in async initialization", e)
                }

                // Fallback to synchronous initialization only if fragment is still attached
                if (isAdded && _binding != null) {
                    try {
                        initializeHelpers()
                        infoButtonManager.setupInfoButtonListeners(
                            resetSessionCallback = { resetSession() },
                            getCurrentSession = { viewModel.uiState.value.currentSession },
                            getBatteryCapacity = { viewModel.uiState.value.batteryCapacityMah }
                        )
                    } catch (fallbackException: Exception) {
                        Log.e(TAG, "Error in fallback initialization", fallbackException)
                    }
                }
            }
        }
    }
    
    private fun observeUiState() {
        Log.d(TAG, "DischargeFragment: Setting up UI state observation")
        Log.d(TAG, "CoreBatteryStatsService: Establishing data subscription pipeline")

        // Primary UI updates - tied to fragment lifecycle for normal operation
        viewLifecycleOwner.lifecycleScope.launch {
            Log.d(TAG, "DischargeFragment: UI state observation coroutine started")
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                Log.d(TAG, "DischargeFragment: Entering STARTED lifecycle state - beginning data collection")
                Log.d(TAG, "Navigation: Fragment ready to receive and display data updates")

                viewModel.uiState.collect { state ->
                    Log.d(TAG, "VM_STATE: Update from ViewModel: percentage=${state.batteryPercentage}, loading=${state.isLoadingInitial}, charging=${state.isCharging}")
                    Log.d(TAG, "CoreBatteryStatsService: Data received - session=${state.currentSession?.isActive}, capacity=${state.batteryCapacityMah}")
                    Log.d(TAG, "DischargeFragment: Processing UI state update - fragment instance: ${<EMAIL>()}")

                    // Ensure binding is valid before UI updates
                    if (!ensureBindingValid()) {
                        Log.w(TAG, "VM_STATE: Skipping UI update - binding not valid")
                        Log.w(TAG, "Navigation: UI update skipped due to invalid binding state")
                        return@collect
                    }

                    // AnimationHelper will call uiUpdater.updateStatusAndEstimates
                    animationHelper.animateBatteryUpdate(state)

                    // Update other UI sections that depend on the full state
                    uiUpdater.updateLossOfCharge(state)
                    uiUpdater.updateCurrentSessionDetails(state)

                    Log.d(TAG, "DischargeFragment: UI update completed successfully")
                }
            }
        }

        // Background UI updates - independent of fragment lifecycle for app resume scenarios
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                appLifecycleManager.appState.collect { appState ->
                    Log.d(TAG, "APP_STATE: App state changed to $appState")

                    // Trigger UI update when app comes to foreground and fragment is inactive
                    if (appLifecycleManager.shouldTriggerUiUpdate()) {
                        Log.i(TAG, "BACKGROUND_UPDATE: Triggering UI update due to app state change")

                        // Get current state and update UI
                        val currentState = viewModel.uiState.value
                        if (_binding != null) {
                            uiUpdater.updateLossOfCharge(currentState)
                            uiUpdater.updateCurrentSessionDetails(currentState)
                        }
                    }
                }
            }
        }
    }

    /**
     * Observes navigation state changes from SharedNavigationViewModel.
     * This replaces the external FragmentLifecycleOptimizer with self-managed fragment state.
     */
    private fun observeNavigationState() {
        Log.d(TAG, "SharedNavigationViewModel: Setting up navigation state observation")

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                sharedNavigationViewModel.activeFragmentId.collect { activeFragmentId ->
                    val isThisFragmentActive = activeFragmentId == R.id.dischargeFragment

                    Log.d(TAG, "SharedNavigationViewModel: Navigation state changed - activeFragment: ${getFragmentName(activeFragmentId)}")
                    Log.d(TAG, "SharedNavigationViewModel: DischargeFragment is ${if (isThisFragmentActive) "ACTIVE" else "INACTIVE"}")

                    if (isThisFragmentActive) {
                        onFragmentVisible()
                    } else {
                        onFragmentHidden()
                    }
                }
            }
        }
    }

    /**
     * Called when this fragment becomes visible/active.
     * Integrates with existing AppLifecycleManager and triggers UI refresh if needed.
     */
    private fun onFragmentVisible() {
        Log.d(TAG, "SharedNavigationViewModel: DischargeFragment is now VISIBLE")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment activated via SharedNavigationViewModel")

        // Integrate with existing AppLifecycleManager
        appLifecycleManager.setDischargeFragmentActive(true)

        // Trigger UI refresh to prevent staleness
        if (_binding != null && !viewModel.uiState.value.isLoadingInitial) {
            Log.d(TAG, "SharedNavigationViewModel: Triggering UI refresh on fragment activation")
            val currentState = viewModel.uiState.value
            uiUpdater.updateStatusAndEstimates(currentState)
            uiUpdater.updateLossOfCharge(currentState)
            uiUpdater.updateCurrentSessionDetails(currentState)
        }

        // Notify UI updater about visibility change
        uiUpdater.onFragmentResumed()
    }

    /**
     * Called when this fragment becomes hidden/inactive.
     * Integrates with existing AppLifecycleManager for proper state management.
     */
    private fun onFragmentHidden() {
        Log.d(TAG, "SharedNavigationViewModel: DischargeFragment is now HIDDEN")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment deactivated via SharedNavigationViewModel")

        // Integrate with existing AppLifecycleManager
        appLifecycleManager.setDischargeFragmentActive(false)

        // Notify UI updater about visibility change
        uiUpdater.onFragmentPaused()
    }

    /**
     * Gets a human-readable fragment name for logging.
     */
    private fun getFragmentName(fragmentId: Int): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }

    /**
     * Ensures view binding is valid and ready for UI updates
     * This helps prevent UI staleness issues
     */
    private fun ensureBindingValid(): Boolean {
        if (_binding == null) {
            Log.w(TAG, "BINDING_CHECK: View binding is null")
            return false
        }

        if (!isAdded) {
            Log.w(TAG, "BINDING_CHECK: Fragment is not added to activity")
            return false
        }

        if (view == null) {
            Log.w(TAG, "BINDING_CHECK: Fragment view is null")
            return false
        }

        // Check if key UI elements are accessible
        try {
            val percentageView = binding.includeStatusAndEstimates.saeTvPercentage
            if (percentageView.parent == null) {
                Log.w(TAG, "BINDING_CHECK: Percentage view is not attached to parent")
                return false
            }
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_CHECK: Error accessing UI elements", e)
            return false
        }

        return true
    }

    /**
     * SERVICE_CONNECTION_FIX: Validates and recovers service connections on fragment resume.
     * Ensures CoreBatteryStatsService connection is active and data flow is working.
     */
    private fun validateAndRecoverServiceConnections() {
        val validationStartTime = System.currentTimeMillis()
        Log.d(TAG, "SERVICE_INVESTIGATION: === SERVICE CONNECTION VALIDATION START ===")
        Log.d(TAG, "SERVICE_INVESTIGATION: Validation started at: $validationStartTime")

        viewLifecycleOwner.lifecycleScope.launch {
            try {
                // Check if we're receiving battery status updates
                val currentState = viewModel.uiState.value

                Log.d(TAG, "SERVICE_INVESTIGATION: === CURRENT UI STATE ANALYSIS ===")
                Log.d(TAG, "SERVICE_INVESTIGATION: Battery percentage: ${currentState.batteryPercentage}%")
                Log.d(TAG, "SERVICE_INVESTIGATION: Charging state: ${currentState.isCharging}")
                Log.d(TAG, "SERVICE_INVESTIGATION: Loading initial: ${currentState.isLoadingInitial}")
                Log.d(TAG, "SERVICE_INVESTIGATION: Loading current session: ${currentState.isCurrentSessionLoading}")
                Log.d(TAG, "SERVICE_INVESTIGATION: Time estimations loading: ${currentState.areTimeEstimationsLoading}")
                Log.d(TAG, "SERVICE_INVESTIGATION: Current session: ${currentState.currentSession?.isActive ?: "null"}")
                Log.d(TAG, "SERVICE_INVESTIGATION: Battery capacity: ${currentState.batteryCapacityMah} mAh")

                // Check ViewModel state
                Log.d(TAG, "SERVICE_INVESTIGATION: === VIEWMODEL STATE ANALYSIS ===")
                Log.d(TAG, "SERVICE_INVESTIGATION: ViewModel class: ${viewModel.javaClass.simpleName}")
                Log.d(TAG, "SERVICE_INVESTIGATION: ViewModel hashCode: ${viewModel.hashCode()}")

                // Check repository connections through reflection if possible
                try {
                    val repositoryField = viewModel.javaClass.getDeclaredField("batteryRepository")
                    repositoryField.isAccessible = true
                    val repository = repositoryField.get(viewModel)
                    Log.d(TAG, "SERVICE_INVESTIGATION: Repository instance: ${repository?.javaClass?.simpleName}")
                    Log.d(TAG, "SERVICE_INVESTIGATION: Repository hashCode: ${repository?.hashCode()}")
                } catch (e: Exception) {
                    Log.w(TAG, "SERVICE_INVESTIGATION: Could not access repository via reflection: ${e.message}")
                }

                // If we're still in loading state after fragment resume, there might be a connection issue
                if (currentState.isLoadingInitial) {
                    Log.w(TAG, "SERVICE_INVESTIGATION: ⚠️ Fragment resumed but still in loading state - potential service connection issue")
                    Log.w(TAG, "SERVICE_INVESTIGATION: This suggests data flow from CoreBatteryStatsService is interrupted")

                    // Wait a bit more and check again
                    Log.d(TAG, "SERVICE_INVESTIGATION: Waiting 2 seconds for potential delayed data...")
                    delay(2000)
                    val stateAfterDelay = viewModel.uiState.value

                    Log.d(TAG, "SERVICE_INVESTIGATION: === STATE AFTER DELAY ===")
                    Log.d(TAG, "SERVICE_INVESTIGATION: Loading initial after delay: ${stateAfterDelay.isLoadingInitial}")
                    Log.d(TAG, "SERVICE_INVESTIGATION: Battery percentage after delay: ${stateAfterDelay.batteryPercentage}%")
                    Log.d(TAG, "SERVICE_INVESTIGATION: Charging state after delay: ${stateAfterDelay.isCharging}")

                    if (stateAfterDelay.isLoadingInitial) {
                        Log.e(TAG, "SERVICE_INVESTIGATION: ❌ Still in loading state after delay - service connection likely lost")
                        Log.e(TAG, "SERVICE_INVESTIGATION: Data pipeline from CoreBatteryStatsService → Repository → ViewModel → UI is broken")

                        // Trigger ViewModel to request fresh data
                        Log.d(TAG, "SERVICE_INVESTIGATION: Attempting service recovery via ViewModel reset...")
                        viewModel.resetSessionData() // This will trigger a fresh data request

                        // Wait and check if recovery worked
                        delay(1000)
                        val stateAfterRecovery = viewModel.uiState.value
                        Log.d(TAG, "SERVICE_INVESTIGATION: === STATE AFTER RECOVERY ATTEMPT ===")
                        Log.d(TAG, "SERVICE_INVESTIGATION: Loading initial after recovery: ${stateAfterRecovery.isLoadingInitial}")
                        Log.d(TAG, "SERVICE_INVESTIGATION: Battery percentage after recovery: ${stateAfterRecovery.batteryPercentage}%")

                        if (stateAfterRecovery.isLoadingInitial) {
                            Log.e(TAG, "SERVICE_INVESTIGATION: ❌ Recovery attempt failed - service connection still lost")
                        } else {
                            Log.d(TAG, "SERVICE_INVESTIGATION: ✅ Recovery successful - service connection restored")
                        }

                    } else {
                        Log.d(TAG, "SERVICE_INVESTIGATION: ✅ Service connection recovered after delay")
                    }
                } else {
                    Log.d(TAG, "SERVICE_INVESTIGATION: ✅ Service connection appears healthy")
                    Log.d(TAG, "SERVICE_INVESTIGATION: Data flow from CoreBatteryStatsService is working correctly")
                }

                val validationEndTime = System.currentTimeMillis()
                val validationDuration = validationEndTime - validationStartTime
                Log.d(TAG, "SERVICE_INVESTIGATION: === SERVICE CONNECTION VALIDATION COMPLETED ===")
                Log.d(TAG, "SERVICE_INVESTIGATION: Validation duration: ${validationDuration}ms")

            } catch (e: Exception) {
                Log.e(TAG, "SERVICE_INVESTIGATION: ❌ Exception during service connection validation", e)
                Log.e(TAG, "SERVICE_INVESTIGATION: Exception type: ${e.javaClass.simpleName}")
                Log.e(TAG, "SERVICE_INVESTIGATION: Exception message: ${e.message}")
                Log.d(TAG, "SERVICE_INVESTIGATION: === SERVICE CONNECTION VALIDATION FAILED ===")
            }
        }
    }

    fun resetSession() {
        Log.d(TAG, "resetSession() called by UI - Triggering ViewModel to reset session data.")
        viewModel.resetSessionData()
    }
}
