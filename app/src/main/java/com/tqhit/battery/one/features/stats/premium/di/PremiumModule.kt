package com.tqhit.battery.one.features.stats.premium.di

import com.tqhit.battery.one.features.stats.premium.cache.PremiumCache
import com.tqhit.battery.one.features.stats.premium.cache.PremiumPrefsCache
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class PremiumModule {

    @Binds
    @Singleton
    abstract fun bindPremiumCache(
        impl: PremiumPrefsCache
    ): PremiumCache
}
