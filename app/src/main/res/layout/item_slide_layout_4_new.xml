<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
    android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

    <androidx.media3.ui.PlayerView
        android:background="?attr/white"
        app:resize_mode="fill"
        android:id="@+id/playerView"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        app:shutter_background_color="?attr/white"
        android:alpha="0.4"/>

        <LinearLayout
            android:id="@+id/date_time_container"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/_80sdp">

            <TextView
                android:id="@+id/text_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="19:00"
                android:textSize="@dimen/_36ssp"
                android:textColor="@android:color/white"
                android:textStyle="bold"/>

            <TextView
                android:id="@+id/text_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Fri, 13 May"
                android:layout_gravity="center_horizontal"
                android:textSize="@dimen/_16ssp"
                android:textColor="@android:color/white"/>
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/step_1_2_choose_your_animation"
            android:layout_gravity="top|center_horizontal"
            android:textSize="@dimen/_15ssp"
            android:textColor="?attr/black"
            android:layout_marginTop="@dimen/_12sdp"/>

        <!-- Overlay làm mờ video -->
        <View
            android:id="@+id/blurOverlay"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#80000000"/>


        <RelativeLayout
            android:layout_marginBottom="@dimen/_10sdp"
            android:layout_gravity="bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/secondary_action"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_30sdp"
                android:paddingHorizontal="@dimen/_10sdp"
                android:text="@string/see_more"
                android:textColor="?attr/black"
                android:textSize="@dimen/_11ssp"
                android:gravity="center"
                android:textStyle="bold"
                android:layout_marginEnd="@dimen/_10sdp"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@id/primary_layout"/>

            <LinearLayout
                android:id="@+id/primary_layout"
                android:layout_width="@dimen/_120sdp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="9dp"
                android:layout_alignParentEnd="true"
                android:animateLayoutChanges="true"
                android:background="@drawable/white_block"
                android:orientation="vertical">
                <Button
                    android:id="@+id/primary_action"
                    android:textSize="@dimen/_15ssp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:textColor="?attr/black"
                    android:background="@drawable/grey_block"
                    android:paddingTop="12.5dp"
                    android:paddingBottom="12.5dp"
                    android:longClickable="false"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:text="@string/accept"
                    android:textAllCaps="false"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    style="@style/Widget.AppCompat.Button.Borderless"/>
            </LinearLayout>
        </RelativeLayout>
    </FrameLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/nativeAd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/grey" />
</LinearLayout>
