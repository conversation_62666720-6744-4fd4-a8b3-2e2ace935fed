<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/white">

    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_alignParentStart="true"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:src="@drawable/ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/un_premium_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/anim_vip"
            android:layout_width="@dimen/_150sdp"
            android:layout_height="@dimen/_150sdp"
            android:layout_marginTop="@dimen/_20sdp"
            android:background="@android:color/transparent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/vip_anim" />

        <TextView
            android:textColor="?attr/black"
            android:id="@+id/tv_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_10sdp"
            android:gravity="center"
            android:text="1.99$/day"
            android:textSize="@dimen/_14ssp"
            app:layout_constraintTop_toBottomOf="@id/anim_vip" />

        <TextView
            android:textColor="?attr/black"
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20sdp"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:gravity="center"
            android:textStyle="bold"
            android:text="@string/free_trial_premium_feature"
            android:textSize="@dimen/_18ssp"
            app:layout_constraintTop_toBottomOf="@id/tv_price" />

        <TextView
            android:textColor="?attr/black"

            android:id="@+id/tv_content1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20sdp"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:gravity="center"
            android:text="@string/remove_ads_in_next_24h"
            android:textSize="@dimen/_12ssp"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <TextView
            android:textColor="?attr/black"

            android:id="@+id/tv_content2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20sdp"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:gravity="center"
            android:text="@string/unlock_premium_theme_in_the_next_24h"
            android:textSize="@dimen/_12ssp"
            app:layout_constraintTop_toBottomOf="@id/tv_content1" />




        <TextView
            android:textColor="?attr/black"

            android:id="@+id/count_ads"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20sdp"
            android:layout_marginBottom="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:gravity="center"
            android:text="@string/watched_format"
            android:textSize="@dimen/_12ssp"
            app:layout_constraintBottom_toTopOf="@id/btn_watch" />

        <Button
            android:id="@+id/btn_watch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_30sdp"
            android:layout_marginEnd="@dimen/_30sdp"
            android:layout_marginBottom="@dimen/_20sdp"
            android:padding="@dimen/_10sdp"
            android:text="@string/watch_ads"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:textSize="@dimen/_14ssp"
            android:background="@drawable/green_button"
            android:textColor="?attr/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>