package com.tqhit.battery.one.dialog.watchads

import android.content.Context
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.databinding.DialogWatchRewardedAdBinding
import dagger.hilt.android.qualifiers.ActivityContext

class WatchRewardedAdDialog(
    @ActivityContext private val context: Context,
    private val onContinue: () -> Unit = {},
    private val onClose: () -> Unit = {},
    private val onRemoveAds: () -> Unit = {},
) : AdLibBaseDialog<DialogWatchRewardedAdBinding>(context) {
    override val binding by lazy { DialogWatchRewardedAdBinding.inflate(layoutInflater) }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams

        setCanceledOnTouchOutside(false)
        setCancelable(false) // Prevent dismissal by back button
    }

    override fun setupListener() {
        super.setupListener()
        binding.btnClose.setOnClickListener {
            onClose()
            dismiss()
        }

        // Allow button - request battery optimization permission
        binding.btnAllow.setOnClickListener {
            onContinue()
            dismiss()
        }

        binding.btnRemoveAds.setOnClickListener {
            onRemoveAds()
            dismiss()
        }

    }

    override fun setupUI() {
        super.setupUI()
    }
}